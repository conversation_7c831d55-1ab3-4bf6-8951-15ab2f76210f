"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SummarizationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SummarizationService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("axios");
let SummarizationService = SummarizationService_1 = class SummarizationService {
    logger = new common_1.Logger(SummarizationService_1.name);
    groqApiKey = process.env.GROQ_API_KEY;
    groqModel = 'llama-3.3-70b-versatile';
    async summarizeText(content, context, fileName) {
        const startTime = Date.now();
        try {
            if (!this.groqApiKey) {
                throw new Error('Clé API Groq non configurée');
            }
            const preparedContent = this.prepareContentForSummarization(content);
            const summary = await this.generateSummary(preparedContent, context, fileName);
            const keyPoints = await this.extractKeyPoints(preparedContent, context);
            const processingTime = Date.now() - startTime;
            return {
                summary,
                keyPoints,
                wordCount: {
                    original: this.countWords(content),
                    summary: this.countWords(summary)
                },
                metadata: {
                    processingTime,
                    summarizedAt: new Date(),
                    context: context || fileName
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de la summarisation: ${error.message}`);
            throw new Error(`Impossible de générer le résumé: ${error.message}`);
        }
    }
    async generateSummary(content, context, fileName) {
        const contextInfo = context || fileName ? ` (${context || fileName})` : '';
        const systemPrompt = `Tu es un expert en analyse et résumé de documents. Ta tâche est de créer un résumé concis et informatif du contenu fourni.

Instructions:
- Crée un résumé structuré et cohérent
- Identifie les idées principales et les concepts clés
- Maintiens le ton et le style appropriés au contenu
- Assure-toi que le résumé soit environ 20-30% de la longueur originale
- Utilise des phrases claires et bien construites
- Préserve les informations importantes et les données factuelles`;
        const userPrompt = `Résume le contenu suivant${contextInfo}:

${content}

Fournis un résumé structuré qui capture l'essence du document tout en restant concis et informatif.`;
        try {
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: userPrompt }
                ],
                temperature: 0.3,
                max_tokens: 2000,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 30000,
            });
            return response.data?.choices?.[0]?.message?.content || 'Résumé non disponible';
        }
        catch (error) {
            this.logger.error(`Erreur lors de la génération du résumé: ${error.message}`);
            throw error;
        }
    }
    async extractKeyPoints(content, context) {
        const systemPrompt = `Tu es un expert en analyse de contenu. Ta tâche est d'extraire les points clés les plus importants du texte fourni.

Instructions:
- Identifie 5-8 points clés maximum
- Chaque point doit être concis (1-2 phrases)
- Concentre-toi sur les informations les plus importantes
- Évite la redondance
- Utilise des puces ou des phrases courtes`;
        const userPrompt = `Extrait les points clés du contenu suivant:

${content}

Fournis une liste des points les plus importants, chacun sur une ligne séparée, précédé d'un tiret (-).`;
        try {
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: userPrompt }
                ],
                temperature: 0.2,
                max_tokens: 1000,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 20000,
            });
            const keyPointsText = response.data?.choices?.[0]?.message?.content || '';
            return this.parseKeyPoints(keyPointsText);
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction des points clés: ${error.message}`);
            return ['Points clés non disponibles'];
        }
    }
    parseKeyPoints(text) {
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        const keyPoints = [];
        for (const line of lines) {
            const cleaned = line
                .replace(/^[-•*]\s*/, '')
                .replace(/^\d+\.\s*/, '')
                .trim();
            if (cleaned.length > 10) {
                keyPoints.push(cleaned);
            }
        }
        return keyPoints.slice(0, 8);
    }
    prepareContentForSummarization(content) {
        let cleaned = content
            .replace(/\s+/g, ' ')
            .replace(/\n{3,}/g, '\n\n')
            .trim();
        const maxLength = 15000;
        if (cleaned.length > maxLength) {
            cleaned = cleaned.substring(0, maxLength) + '...';
            this.logger.warn('Contenu tronqué pour la summarisation');
        }
        return cleaned;
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    async generateQuickSummary(content) {
        if (this.countWords(content) < 100) {
            return content;
        }
        const systemPrompt = 'Résume ce texte en 2-3 phrases maximum, en gardant les informations essentielles.';
        try {
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: content }
                ],
                temperature: 0.3,
                max_tokens: 200,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 10000,
            });
            return response.data?.choices?.[0]?.message?.content || content;
        }
        catch (error) {
            this.logger.error(`Erreur lors du résumé rapide: ${error.message}`);
            return content;
        }
    }
};
exports.SummarizationService = SummarizationService;
exports.SummarizationService = SummarizationService = SummarizationService_1 = __decorate([
    (0, common_1.Injectable)()
], SummarizationService);
//# sourceMappingURL=summarization.service.js.map