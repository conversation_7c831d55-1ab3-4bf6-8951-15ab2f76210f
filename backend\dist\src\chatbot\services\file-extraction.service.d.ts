export interface ExtractedContent {
    text: string;
    metadata?: {
        wordCount?: number;
        pageCount?: number;
        extractedAt?: Date;
        fileSize?: number;
        fileType?: string;
    };
}
export declare class FileExtractionService {
    private readonly logger;
    private readonly supportedTypes;
    getSupportedFileTypes(): string[];
    validateFileType(fileName: string): boolean;
    extractContent(buffer: Buffer, fileName: string, mimeType?: string): Promise<ExtractedContent>;
    private extractFromPDF;
    private extractFromWord;
    private extractFromExcel;
}
