export interface ExtractedContent {
    text: string;
    metadata?: {
        pageCount?: number;
        wordCount?: number;
        fileSize?: number;
        extractedAt: Date;
    };
}
export declare class FileExtractionService {
    private readonly logger;
    extractContent(buffer: Buffer, fileName: string, mimeType?: string): Promise<ExtractedContent>;
    private extractFromPdf;
    private extractFromWord;
    private extractFromExcel;
    private extractFromText;
    private getFileExtension;
    private countWords;
    validateFileType(fileName: string): boolean;
    getSupportedFileTypes(): string[];
}
