{"name": "backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed": "prisma db seed"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.1.0", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@prisma/client": "^6.7.0", "@types/pdf-parse": "^1.1.5", "axios": "^1.9.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "express": "^5.1.0", "imap": "^0.8.19", "mailparser": "^3.7.3", "mammoth": "^1.9.1", "multer": "^1.4.5-lts.2", "nestjs-prisma": "^0.25.0", "node-imap": "^0.9.6", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-parse": "^1.1.1", "postgres": "^3.4.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "twilio": "^5.7.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/imap": "^0.8.42", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.5.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}