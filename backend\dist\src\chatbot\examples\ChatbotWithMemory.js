"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const react_1 = require("react");
const ChatbotWithMemory = ({ userId, apiBaseUrl = '/api' }) => {
    const [messages, setMessages] = (0, react_1.useState)([]);
    const [inputMessage, setInputMessage] = (0, react_1.useState)('');
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [sessionId] = (0, react_1.useState)(() => `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
    const messagesEndRef = (0, react_1.useRef)(null);
    (0, react_1.useEffect)(() => {
        loadHistory();
    }, []);
    (0, react_1.useEffect)(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);
    const loadHistory = async () => {
        try {
            const response = await fetch(`${apiBaseUrl}/chatbot/memory/history`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sessionId,
                    userId,
                    limit: 20
                })
            });
            const data = await response.json();
            if (data.success && data.history.length > 0) {
                const historyMessages = data.history.map((item) => ({
                    id: item.id,
                    userMessage: item.userMessage,
                    botResponse: item.botResponse,
                    createdAt: item.createdAt,
                    isFromHistory: true
                }));
                setMessages(historyMessages);
            }
        }
        catch (error) {
            console.error('Erreur lors du chargement de l\'historique:', error);
        }
    };
    const sendMessage = async () => {
        if (!inputMessage.trim() || isLoading)
            return;
        const userMessage = inputMessage.trim();
        setInputMessage('');
        setIsLoading(true);
        try {
            const response = await fetch(`${apiBaseUrl}/chatbot/message`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: userMessage,
                    sessionId,
                    userId
                })
            });
            const data = await response.json();
            const newMessage = {
                id: Date.now(),
                userMessage,
                botResponse: data.response,
                createdAt: new Date().toISOString(),
                isFromHistory: false
            };
            setMessages(prev => [...prev, newMessage]);
        }
        catch (error) {
            console.error('Erreur lors de l\'envoi du message:', error);
            const errorMessage = {
                id: Date.now(),
                userMessage,
                botResponse: 'Désolé, une erreur s\'est produite. Veuillez réessayer.',
                createdAt: new Date().toISOString(),
                isFromHistory: false
            };
            setMessages(prev => [...prev, errorMessage]);
        }
        finally {
            setIsLoading(false);
        }
    };
    const clearMemory = async () => {
        try {
            const response = await fetch(`${apiBaseUrl}/chatbot/memory/clear`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    sessionId,
                    userId
                })
            });
            const data = await response.json();
            if (data.success) {
                setMessages([]);
                alert('Mémoire effacée avec succès !');
            }
        }
        catch (error) {
            console.error('Erreur lors de l\'effacement de la mémoire:', error);
            alert('Erreur lors de l\'effacement de la mémoire');
        }
    };
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };
    return (<div className="chatbot-container" style={{
            maxWidth: '600px',
            margin: '0 auto',
            border: '1px solid #ddd',
            borderRadius: '8px',
            height: '500px',
            display: 'flex',
            flexDirection: 'column'
        }}>
      
      <div style={{
            padding: '16px',
            borderBottom: '1px solid #ddd',
            backgroundColor: '#f8f9fa',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
        }}>
        <h3 style={{ margin: 0 }}>Assistant MKA LMS</h3>
        <div>
          <button onClick={clearMemory} style={{
            padding: '4px 8px',
            fontSize: '12px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
        }}>
            Effacer mémoire
          </button>
        </div>
      </div>

      
      <div style={{
            flex: 1,
            padding: '16px',
            overflowY: 'auto',
            backgroundColor: '#fff'
        }}>
        {messages.length === 0 ? (<div style={{
                textAlign: 'center',
                color: '#666',
                fontStyle: 'italic'
            }}>
            Aucun message. Commencez une conversation !
          </div>) : (messages.map((message) => (<div key={message.id} style={{ marginBottom: '16px' }}>
              {message.isFromHistory && (<div style={{
                    fontSize: '10px',
                    color: '#999',
                    marginBottom: '4px'
                }}>
                  📚 Historique - {new Date(message.createdAt).toLocaleString()}
                </div>)}
              
              
              <div style={{
                marginBottom: '8px',
                textAlign: 'right'
            }}>
                <div style={{
                display: 'inline-block',
                padding: '8px 12px',
                backgroundColor: '#007bff',
                color: 'white',
                borderRadius: '18px',
                maxWidth: '70%',
                textAlign: 'left'
            }}>
                  {message.userMessage}
                </div>
              </div>

              
              <div style={{
                textAlign: 'left'
            }}>
                <div style={{
                display: 'inline-block',
                padding: '8px 12px',
                backgroundColor: '#f1f3f4',
                borderRadius: '18px',
                maxWidth: '70%',
                whiteSpace: 'pre-wrap'
            }}>
                  {message.botResponse}
                </div>
              </div>
            </div>)))}
        
        {isLoading && (<div style={{
                textAlign: 'center',
                color: '#666',
                fontStyle: 'italic'
            }}>
            🤖 L'assistant réfléchit...
          </div>)}
        
        <div ref={messagesEndRef}/>
      </div>

      
      <div style={{
            padding: '16px',
            borderTop: '1px solid #ddd',
            backgroundColor: '#f8f9fa'
        }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <textarea value={inputMessage} onChange={(e) => setInputMessage(e.target.value)} onKeyPress={handleKeyPress} placeholder="Tapez votre message..." style={{
            flex: 1,
            padding: '8px 12px',
            border: '1px solid #ddd',
            borderRadius: '20px',
            resize: 'none',
            minHeight: '40px',
            maxHeight: '100px'
        }} disabled={isLoading}/>
          <button onClick={sendMessage} disabled={!inputMessage.trim() || isLoading} style={{
            padding: '8px 16px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            cursor: 'pointer',
            minWidth: '80px'
        }}>
            {isLoading ? '...' : 'Envoyer'}
          </button>
        </div>
        
        <div style={{
            fontSize: '11px',
            color: '#666',
            marginTop: '4px',
            textAlign: 'center'
        }}>
          Session: {sessionId.slice(-8)} | 
          {userId ? ` Utilisateur: ${userId} |` : ''} 
          {messages.length} message(s)
        </div>
      </div>
    </div>);
};
exports.default = ChatbotWithMemory;
//# sourceMappingURL=ChatbotWithMemory.js.map