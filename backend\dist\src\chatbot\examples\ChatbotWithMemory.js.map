{"version": 3, "file": "ChatbotWithMemory.js", "sourceRoot": "", "sources": ["../../../../src/chatbot/examples/ChatbotWithMemory.tsx"], "names": [], "mappings": ";;AAAA,iCAA2D;AAe3D,MAAM,iBAAiB,GAAqC,CAAC,EAC3D,MAAM,EACN,UAAU,GAAG,MAAM,EACpB,EAAE,EAAE;IACH,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,IAAA,gBAAQ,EAAgB,EAAE,CAAC,CAAC;IAC5D,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC;IACrD,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,SAAS,CAAC,GAAG,IAAA,gBAAQ,EAAC,GAAG,EAAE,CAChC,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CACnE,CAAC;IACF,MAAM,cAAc,GAAG,IAAA,cAAM,EAAiB,IAAI,CAAC,CAAC;IAGpD,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAGP,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;IACjE,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,yBAAyB,EAAE;gBACnE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,SAAS;oBACT,MAAM;oBACN,KAAK,EAAE,EAAE;iBACV,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACvD,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,aAAa,EAAE,IAAI;iBACpB,CAAC,CAAC,CAAC;gBACJ,WAAW,CAAC,eAAe,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACtE,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,SAAS;YAAE,OAAO;QAE9C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC;QACxC,eAAe,CAAC,EAAE,CAAC,CAAC;QACpB,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,kBAAkB,EAAE;gBAC5D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,OAAO,EAAE,WAAW;oBACpB,SAAS;oBACT,MAAM;iBACP,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEnC,MAAM,UAAU,GAAgB;gBAC9B,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;gBACd,WAAW;gBACX,WAAW,EAAE,IAAI,CAAC,QAAQ;gBAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,aAAa,EAAE,KAAK;aACrB,CAAC;YAEF,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,YAAY,GAAgB;gBAChC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;gBACd,WAAW;gBACX,WAAW,EAAE,yDAAyD;gBACtE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,aAAa,EAAE,KAAK;aACrB,CAAC;YACF,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;QAC/C,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,uBAAuB,EAAE;gBACjE,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,SAAS;oBACT,MAAM;iBACP,CAAC;aACH,CAAC,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,WAAW,CAAC,EAAE,CAAC,CAAC;gBAChB,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACpE,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,CAAsB,EAAE,EAAE;QAChD,IAAI,CAAC,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrC,CAAC,CAAC,cAAc,EAAE,CAAC;YACnB,WAAW,EAAE,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,CAAC,GAAG,CAAC,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACxC,QAAQ,EAAE,OAAO;YACjB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,gBAAgB;YACxB,YAAY,EAAE,KAAK;YACnB,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,MAAM;YACf,aAAa,EAAE,QAAQ;SACxB,CAAC,CACA;MACA;MAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACV,OAAO,EAAE,MAAM;YACf,YAAY,EAAE,gBAAgB;YAC9B,eAAe,EAAE,SAAS;YAC1B,OAAO,EAAE,MAAM;YACf,cAAc,EAAE,eAAe;YAC/B,UAAU,EAAE,QAAQ;SACrB,CAAC,CACA;QAAA,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,iBAAiB,EAAE,EAAE,CAC/C;QAAA,CAAC,GAAG,CACF;UAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,WAAW,CAAC,CACrB,KAAK,CAAC,CAAC;YACL,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,SAAS;YAC1B,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,KAAK;YACnB,MAAM,EAAE,SAAS;SAClB,CAAC,CAEF;;UACF,EAAE,MAAM,CACV;QAAA,EAAE,GAAG,CACP;MAAA,EAAE,GAAG,CAEL;;MACA;MAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACV,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,MAAM;YACjB,eAAe,EAAE,MAAM;SACxB,CAAC,CACA;QAAA,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CACvB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE,QAAQ;aACpB,CAAC,CACA;;UACF,EAAE,GAAG,CAAC,CACP,CAAC,CAAC,CAAC,CACF,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CACxB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CACpD;cAAA,CAAC,OAAO,CAAC,aAAa,IAAI,CACxB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACV,QAAQ,EAAE,MAAM;oBAChB,KAAK,EAAE,MAAM;oBACb,YAAY,EAAE,KAAK;iBACpB,CAAC,CACA;kCAAgB,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAC/D;gBAAA,EAAE,GAAG,CAAC,CACP,CAED;;cACA;cAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,YAAY,EAAE,KAAK;gBACnB,SAAS,EAAE,OAAO;aACnB,CAAC,CACA;gBAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,UAAU;gBACnB,eAAe,EAAE,SAAS;gBAC1B,KAAK,EAAE,OAAO;gBACd,YAAY,EAAE,MAAM;gBACpB,QAAQ,EAAE,KAAK;gBACf,SAAS,EAAE,MAAM;aAClB,CAAC,CACA;kBAAA,CAAC,OAAO,CAAC,WAAW,CACtB;gBAAA,EAAE,GAAG,CACP;cAAA,EAAE,GAAG,CAEL;;cACA;cAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,SAAS,EAAE,MAAM;aAClB,CAAC,CACA;gBAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,UAAU;gBACnB,eAAe,EAAE,SAAS;gBAC1B,YAAY,EAAE,MAAM;gBACpB,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,UAAU;aACvB,CAAC,CACA;kBAAA,CAAC,OAAO,CAAC,WAAW,CACtB;gBAAA,EAAE,GAAG,CACP;cAAA,EAAE,GAAG,CACP;YAAA,EAAE,GAAG,CAAC,CACP,CAAC,CACH,CAED;;QAAA,CAAC,SAAS,IAAI,CACZ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACV,SAAS,EAAE,QAAQ;gBACnB,KAAK,EAAE,MAAM;gBACb,SAAS,EAAE,QAAQ;aACpB,CAAC,CACA;;UACF,EAAE,GAAG,CAAC,CACP,CAED;;QAAA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,EAC3B;MAAA,EAAE,GAAG,CAEL;;MACA;MAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACV,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,gBAAgB;YAC3B,eAAe,EAAE,SAAS;SAC3B,CAAC,CACA;QAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAC1C;UAAA,CAAC,QAAQ,CACP,KAAK,CAAC,CAAC,YAAY,CAAC,CACpB,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CACjD,UAAU,CAAC,CAAC,cAAc,CAAC,CAC3B,WAAW,CAAC,wBAAwB,CACpC,KAAK,CAAC,CAAC;YACL,IAAI,EAAE,CAAC;YACP,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,gBAAgB;YACxB,YAAY,EAAE,MAAM;YACpB,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,OAAO;SACnB,CAAC,CACF,QAAQ,CAAC,CAAC,SAAS,CAAC,EAEtB;UAAA,CAAC,MAAM,CACL,OAAO,CAAC,CAAC,WAAW,CAAC,CACrB,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC,CAC5C,KAAK,CAAC,CAAC;YACL,OAAO,EAAE,UAAU;YACnB,eAAe,EAAE,SAAS;YAC1B,KAAK,EAAE,OAAO;YACd,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,MAAM;YACpB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,MAAM;SACjB,CAAC,CAEF;YAAA,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAChC;UAAA,EAAE,MAAM,CACV;QAAA,EAAE,GAAG,CAEL;;QAAA,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACV,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE,MAAM;YACb,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,QAAQ;SACpB,CAAC,CACA;mBAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAE;UAC/B,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,CAC1C;UAAA,CAAC,QAAQ,CAAC,MAAM,CAAE;QACpB,EAAE,GAAG,CACP;MAAA,EAAE,GAAG,CACP;IAAA,EAAE,GAAG,CAAC,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,kBAAe,iBAAiB,CAAC"}