import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ChatbotController } from './chatbot.controller';
import { ChatbotService } from './chatbot.service';
import { FileExtractionService } from './services/file-extraction.service';
import { SummarizationService } from './services/summarization.service';

@Module({
  controllers: [ChatbotController],
  providers: [
    ChatbotService,
    FileExtractionService,
    SummarizationService
  ],
})
export class ChatbotModule {}
