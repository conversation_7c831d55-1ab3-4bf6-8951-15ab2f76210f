import { PrismaService } from 'nestjs-prisma';
import { CreateFeedbackDto } from './dto/create-feedback.dto';
import { CreateFeedbackResponseDto } from './dto/create-feedback-response.dto';
import { UpdateFeedbackDto } from './dto/update-feedback.dto';
export declare class FeedbackService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createFeedbackDto: CreateFeedbackDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    findAll(filters?: any): Promise<({
        responses: {
            id: number;
            createdAt: Date;
            response: string;
            feedbackId: number;
            responderId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    })[]>;
    findOne(id: number): Promise<{
        responses: {
            id: number;
            createdAt: Date;
            response: string;
            feedbackId: number;
            responderId: number | null;
        }[];
    } & {
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    update(id: number, updateFeedbackDto: UpdateFeedbackDto): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    remove(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    createResponse(id: number, dto: CreateFeedbackResponseDto): Promise<{
        id: number;
        createdAt: Date;
        response: string;
        feedbackId: number;
        responderId: number | null;
    }>;
    like(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    dislike(id: number): Promise<{
        id: number;
        createdAt: Date;
        updatedAt: Date;
        message: string;
        type: string | null;
        rating: number;
        category: string | null;
        tags: string[];
        likes: number;
        dislikes: number;
        senderId: number | null;
        receiverId: number | null;
    }>;
    getStats(): Promise<{
        totalFeedbacks: number;
        averageRating: number;
        categoryBreakdown: {
            category: string;
            count: unknown;
            percentage: number;
        }[];
        recentFeedbackCount: number;
        pendingResponses: number;
    }>;
    getAnalytics(timeRange?: string): Promise<{
        ratingData: {
            name: string;
            count: any;
        }[];
        categoryData: {
            name: string;
            value: unknown;
        }[];
        timelineData: {
            day: string;
            count: unknown;
        }[] | {
            month: string;
            count: unknown;
        }[];
    }>;
    private filterByTimeRange;
    private generateTimelineData;
}
