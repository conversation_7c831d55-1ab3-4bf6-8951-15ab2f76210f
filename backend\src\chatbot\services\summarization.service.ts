import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

export interface SummaryResult {
  summary: string;
  keyPoints: string[];
  wordCount: {
    original: number;
    summary: number;
  };
  metadata: {
    processingTime: number;
    summarizedAt: Date;
    context?: string;
  };
}

@Injectable()
export class SummarizationService {
  private readonly logger = new Logger(SummarizationService.name);
  private readonly groqApiKey = process.env.GROQ_API_KEY;
  private readonly groqModel = 'llama-3.3-70b-versatile';

  async summarizeText(content: string, context?: string, fileName?: string): Promise<SummaryResult> {
    const startTime = Date.now();
    
    try {
      if (!this.groqApiKey) {
        throw new Error('Clé API Groq non configurée');
      }

      // Préparer le contenu pour la summarisation
      const preparedContent = this.prepareContentForSummarization(content);
      
      // Générer le résumé principal
      const summary = await this.generateSummary(preparedContent, context, fileName);
      
      // Extraire les points clés
      const keyPoints = await this.extractKeyPoints(preparedContent, context);
      
      const processingTime = Date.now() - startTime;
      
      return {
        summary,
        keyPoints,
        wordCount: {
          original: this.countWords(content),
          summary: this.countWords(summary)
        },
        metadata: {
          processingTime,
          summarizedAt: new Date(),
          context: context || fileName
        }
      };
    } catch (error) {
      this.logger.error(`Erreur lors de la summarisation: ${error.message}`);
      throw new Error(`Impossible de générer le résumé: ${error.message}`);
    }
  }

  private async generateSummary(content: string, context?: string, fileName?: string): Promise<string> {
    const contextInfo = context || fileName ? ` (${context || fileName})` : '';
    
    const systemPrompt = `Tu es un expert en analyse et résumé de documents. Ta tâche est de créer un résumé concis et informatif du contenu fourni.

Instructions:
- Crée un résumé structuré et cohérent
- Identifie les idées principales et les concepts clés
- Maintiens le ton et le style appropriés au contenu
- Assure-toi que le résumé soit environ 20-30% de la longueur originale
- Utilise des phrases claires et bien construites
- Préserve les informations importantes et les données factuelles`;

    const userPrompt = `Résume le contenu suivant${contextInfo}:

${content}

Fournis un résumé structuré qui capture l'essence du document tout en restant concis et informatif.`;

    try {
      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.3,
          max_tokens: 2000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || 'Résumé non disponible';
    } catch (error) {
      this.logger.error(`Erreur lors de la génération du résumé: ${error.message}`);
      throw error;
    }
  }

  private async extractKeyPoints(content: string, context?: string): Promise<string[]> {
    const systemPrompt = `Tu es un expert en analyse de contenu. Ta tâche est d'extraire les points clés les plus importants du texte fourni.

Instructions:
- Identifie 5-8 points clés maximum
- Chaque point doit être concis (1-2 phrases)
- Concentre-toi sur les informations les plus importantes
- Évite la redondance
- Utilise des puces ou des phrases courtes`;

    const userPrompt = `Extrait les points clés du contenu suivant:

${content}

Fournis une liste des points les plus importants, chacun sur une ligne séparée, précédé d'un tiret (-).`;

    try {
      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.2,
          max_tokens: 1000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 20000,
        }
      );

      const keyPointsText = response.data?.choices?.[0]?.message?.content || '';
      
      // Parser les points clés
      return this.parseKeyPoints(keyPointsText);
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction des points clés: ${error.message}`);
      return ['Points clés non disponibles'];
    }
  }

  private parseKeyPoints(text: string): string[] {
    const lines = text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    
    const keyPoints: string[] = [];
    
    for (const line of lines) {
      // Nettoyer les puces et formatage
      const cleaned = line
        .replace(/^[-•*]\s*/, '')
        .replace(/^\d+\.\s*/, '')
        .trim();
      
      if (cleaned.length > 10) { // Ignorer les lignes trop courtes
        keyPoints.push(cleaned);
      }
    }
    
    return keyPoints.slice(0, 8); // Limiter à 8 points maximum
  }

  private prepareContentForSummarization(content: string): string {
    // Nettoyer et préparer le contenu
    let cleaned = content
      .replace(/\s+/g, ' ') // Normaliser les espaces
      .replace(/\n{3,}/g, '\n\n') // Limiter les sauts de ligne multiples
      .trim();
    
    // Limiter la longueur si nécessaire (pour éviter les timeouts)
    const maxLength = 15000; // Environ 3000 mots
    if (cleaned.length > maxLength) {
      cleaned = cleaned.substring(0, maxLength) + '...';
      this.logger.warn('Contenu tronqué pour la summarisation');
    }
    
    return cleaned;
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  async generateQuickSummary(content: string): Promise<string> {
    // Version rapide pour les petits contenus
    if (this.countWords(content) < 100) {
      return content; // Pas besoin de résumer si c'est déjà court
    }

    const systemPrompt = 'Résume ce texte en 2-3 phrases maximum, en gardant les informations essentielles.';
    
    try {
      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: content }
          ],
          temperature: 0.3,
          max_tokens: 200,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 10000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || content;
    } catch (error) {
      this.logger.error(`Erreur lors du résumé rapide: ${error.message}`);
      return content;
    }
  }
}
