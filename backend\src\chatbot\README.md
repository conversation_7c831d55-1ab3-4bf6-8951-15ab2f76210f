# Chatbot avec Traitement de Fichiers et Résumés

## Vue d'ensemble

Le chatbot MKA LMS a été amélioré avec des fonctionnalités avancées de traitement de fichiers et de génération de résumés intelligents. Il peut maintenant :

- Extraire le contenu de différents types de fichiers
- Générer des résumés contextuels et intelligents
- Identifier les points clés des documents
- Analyser et présenter les statistiques de contenu

## Fonctionnalités

### 1. Traitement de Fichiers

**Formats supportés :**
- PDF (`.pdf`)
- Documents Word (`.docx`, `.doc`)
- <PERSON><PERSON><PERSON> Excel (`.xlsx`, `.xls`)
- Fichiers texte (`.txt`)

**Limites :**
- Taille maximale : 10MB
- Timeout : 30 secondes pour la génération de résumés

### 2. Génération de Résumés

- **Résumé complet** : Analyse détaillée avec points clés
- **Résumé rapide** : Version condensée pour les longs textes
- **Statistiques** : Nombre de mots, réduction de taille, temps de traitement

## API Endpoints

### 1. `/chatbot/upload-file` (POST)

Traite un fichier et génère un résumé complet.

**Paramètres :**
- `file` : Fichier à traiter (multipart/form-data)
- `fileName` (optionnel) : Nom personnalisé pour le fichier
- `context` (optionnel) : Contexte pour améliorer le résumé

**Réponse :**
```json
{
  "success": true,
  "fileName": "document.pdf",
  "fileSize": 1024000,
  "extractedContent": {
    "wordCount": 1500,
    "pageCount": 5,
    "extractedAt": "2025-01-01T12:00:00Z"
  },
  "summary": {
    "summary": "Résumé du document...",
    "keyPoints": ["Point 1", "Point 2"],
    "wordCount": {
      "original": 1500,
      "summary": 300
    },
    "metadata": {
      "processingTime": 2500,
      "summarizedAt": "2025-01-01T12:00:00Z"
    }
  },
  "formattedResponse": "📄 **Résumé du fichier...**"
}
```

### 2. `/chatbot/summarize-text` (POST)

Génère un résumé à partir de texte fourni.

**Paramètres :**
```json
{
  "content": "Texte à résumer...",
  "context": "Contexte optionnel",
  "fileName": "Nom optionnel"
}
```

### 3. `/chatbot/extract-content` (POST)

Extrait uniquement le contenu d'un fichier sans génération de résumé.

**Paramètres :**
- `file` : Fichier à traiter

### 4. `/chatbot/supported-formats` (POST)

Retourne la liste des formats supportés.

## Utilisation dans le Chat

Le chatbot reconnaît maintenant les demandes liées aux fichiers :

**Exemples de commandes :**
- "aide" ou "help" → Aide générale avec nouvelles fonctionnalités
- "fichier" ou "document" → Aide spécifique aux fichiers
- "résumé" ou "summary" → Information sur la génération de résumés

## Architecture Technique

### Services

1. **FileExtractionService** : Extraction de contenu des fichiers
2. **SummarizationService** : Génération de résumés avec Groq AI
3. **ChatbotService** : Orchestration et intégration

### Dépendances

- `pdf-parse` : Extraction PDF
- `mammoth` : Extraction Word
- `xlsx` : Extraction Excel
- `multer` : Upload de fichiers
- `axios` : Communication avec Groq API

## Configuration

Assurez-vous que la variable d'environnement `GROQ_API_KEY` est configurée pour utiliser les fonctionnalités de résumé.

## Exemples d'utilisation

### Upload via cURL

```bash
curl -X POST \
  http://localhost:8000/chatbot/upload-file \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@document.pdf' \
  -F 'context=Document de formation'
```

### Résumé de texte via cURL

```bash
curl -X POST \
  http://localhost:8000/chatbot/summarize-text \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "Votre texte à résumer...",
    "context": "Contexte du document"
  }'
```

## Gestion d'erreurs

Le système gère automatiquement :
- Types de fichiers non supportés
- Fichiers corrompus
- Timeouts de traitement
- Erreurs de l'API Groq
- Fichiers trop volumineux

## Performance

- **Extraction** : < 5 secondes pour la plupart des fichiers
- **Résumé** : 10-30 secondes selon la longueur
- **Mémoire** : Optimisée pour les fichiers jusqu'à 10MB
