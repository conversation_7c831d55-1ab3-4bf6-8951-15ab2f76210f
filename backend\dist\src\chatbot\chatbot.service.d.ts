import { PrismaService } from 'nestjs-prisma';
export declare class ChatbotService {
    private prisma;
    private readonly logger;
    private readonly groqApiKey;
    private readonly groqModel;
    constructor(prisma: PrismaService);
    processMessage(message: string, sessionId?: string, userId?: number): Promise<string>;
    private isGreeting;
    private isHelpRequest;
    private isFileHelpRequest;
    private getEnhancedHelp;
    private getFileProcessingHelp;
    private isStatsRequest;
    private getGeneralStats;
    private handleEntityQueries;
    private isCountQuery;
    private isListQuery;
    private isDetailQuery;
    private askGroq;
    private askGroqWithMemory;
    executeQuery(query: string): Promise<any>;
    saveToMemory(sessionId: string, userId: number | undefined, userMessage: string, botResponse: string, context?: string): Promise<void>;
    getMemoryHistory(sessionId: string, userId?: number, limit?: number): Promise<any[]>;
    clearMemory(sessionId: string, userId?: number): Promise<boolean>;
    getMemoryContext(sessionId: string, userId?: number, limit?: number): Promise<string>;
}
