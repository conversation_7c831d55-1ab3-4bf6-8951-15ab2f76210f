import { PrismaService } from 'nestjs-prisma';
import { FileExtractionService, ExtractedContent } from './services/file-extraction.service';
import { SummarizationService, SummaryResult } from './services/summarization.service';
export declare class ChatbotService {
    private prisma;
    private fileExtractionService;
    private summarizationService;
    private readonly logger;
    private readonly groqApiKey;
    private readonly groqModel;
    constructor(prisma: PrismaService, fileExtractionService: FileExtractionService, summarizationService: SummarizationService);
    processMessage(message: string, sessionId?: string, userId?: number): Promise<string>;
    private isGreeting;
    private isHelpRequest;
    private isFileHelpRequest;
    private getEnhancedHelp;
    private getFileProcessingHelp;
    private isStatsRequest;
    private getGeneralStats;
    private handleEntityQueries;
    private isCountQuery;
    private isListQuery;
    private isDetailQuery;
    private askGroq;
    private askGroqWithMemory;
    executeQuery(query: string): Promise<any>;
    processFileContent(buffer: Buffer, fileName: string, mimeType?: string): Promise<ExtractedContent>;
    summarizeContent(content: string, context?: string, fileName?: string): Promise<SummaryResult>;
    processFileAndSummarize(buffer: Buffer, fileName: string, context?: string, mimeType?: string): Promise<{
        extractedContent: ExtractedContent;
        summary: SummaryResult;
    }>;
    processTextInput(text: string, context?: string): Promise<{
        summary: SummaryResult;
        quickSummary?: string;
    }>;
    formatSummaryResponse(summaryResult: SummaryResult, fileName?: string): string;
    saveToMemory(sessionId: string, userId: number | undefined, userMessage: string, botResponse: string, context?: string): Promise<void>;
    getMemoryHistory(sessionId: string, userId?: number, limit?: number): Promise<any[]>;
    clearMemory(sessionId: string, userId?: number): Promise<boolean>;
    getMemoryContext(sessionId: string, userId?: number, limit?: number): Promise<string>;
}
