import { PrismaService } from 'nestjs-prisma';
export declare class ChatbotService {
    private prisma;
    private readonly logger;
    private readonly groqApiKey;
    private readonly groqModel;
    constructor(prisma: PrismaService);
    processMessage(message: string, sessionId?: string, userId?: number): Promise<string>;
    private isGreeting;
    private isHelpRequest;
    private isFileHelpRequest;
    private getEnhancedHelp;
    private getFileProcessingHelp;
    private isStatsRequest;
    private getGeneralStats;
    private handleEntityQueries;
    private handleComplexQueries;
    private isCountQuery;
    private isListQuery;
    private isSummaryQuery;
    private isDetailQuery;
    private askGroq;
    private askGroqWithMemory;
    executeQuery(query: string): Promise<any>;
    saveToMemory(sessionId: string, userId: number | undefined, userMessage: string, botResponse: string, context?: string): Promise<void>;
    getMemoryHistory(sessionId: string, userId?: number, limit?: number): Promise<any[]>;
    clearMemory(sessionId: string, userId?: number): Promise<boolean>;
    getMemoryContext(sessionId: string, userId?: number, limit?: number): Promise<string>;
    testDatabaseConnection(): Promise<any>;
    getUsers(): Promise<any[]>;
    private formatEntityList;
    private formatEntityDetail;
    private isSearchQuery;
    private extractSearchTerm;
    private searchEntity;
    private formatSearchResults;
    private getAdvancedStats;
    private handleSummaryRequest;
    private summarizeContent;
    private extractFileContent;
    private extractPdfContent;
    private extractWordContent;
    private generateContentSummary;
    private getInstructorInfo;
    private getPopularPrograms;
    private getQuizStats;
    private getRecentActivity;
}
