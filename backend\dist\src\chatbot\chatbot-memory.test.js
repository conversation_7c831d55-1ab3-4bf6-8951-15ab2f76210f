"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const chatbot_service_1 = require("./chatbot.service");
const nestjs_prisma_1 = require("nestjs-prisma");
const file_extraction_service_1 = require("./services/file-extraction.service");
const summarization_service_1 = require("./services/summarization.service");
describe('ChatbotService - Memory Functions', () => {
    let service;
    let prismaService;
    const mockPrismaService = {
        chatMemory: {
            create: jest.fn(),
            findMany: jest.fn(),
            deleteMany: jest.fn(),
        },
        user: {
            count: jest.fn().mockResolvedValue(10),
        },
        program: {
            count: jest.fn().mockResolvedValue(5),
        },
        module: {
            count: jest.fn().mockResolvedValue(15),
        },
        course: {
            count: jest.fn().mockResolvedValue(25),
        },
        contenu: {
            count: jest.fn().mockResolvedValue(50),
        },
        quiz: {
            count: jest.fn().mockResolvedValue(8),
        },
    };
    const mockFileExtractionService = {
        getSupportedFileTypes: jest.fn().mockReturnValue(['pdf', 'docx', 'txt']),
        validateFileType: jest.fn().mockReturnValue(true),
        extractContent: jest.fn(),
    };
    const mockSummarizationService = {
        summarizeText: jest.fn(),
        generateQuickSummary: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                chatbot_service_1.ChatbotService,
                {
                    provide: nestjs_prisma_1.PrismaService,
                    useValue: mockPrismaService,
                },
                {
                    provide: file_extraction_service_1.FileExtractionService,
                    useValue: mockFileExtractionService,
                },
                {
                    provide: summarization_service_1.SummarizationService,
                    useValue: mockSummarizationService,
                },
            ],
        }).compile();
        service = module.get(chatbot_service_1.ChatbotService);
        prismaService = module.get(nestjs_prisma_1.PrismaService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('saveToMemory', () => {
        it('should save conversation to memory', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            const userMessage = 'Bonjour';
            const botResponse = 'Bonjour ! Comment puis-je vous aider ?';
            mockPrismaService.chatMemory.create.mockResolvedValue({
                id: 1,
                sessionId,
                userId,
                userMessage,
                botResponse,
                context: null,
                createdAt: new Date(),
                updatedAt: new Date(),
            });
            await service.saveToMemory(sessionId, userId, userMessage, botResponse);
            expect(mockPrismaService.chatMemory.create).toHaveBeenCalledWith({
                data: {
                    sessionId,
                    userId,
                    userMessage,
                    botResponse,
                    context: undefined,
                },
            });
        });
        it('should handle save errors gracefully', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            const userMessage = 'Bonjour';
            const botResponse = 'Bonjour !';
            mockPrismaService.chatMemory.create.mockRejectedValue(new Error('Database error'));
            await expect(service.saveToMemory(sessionId, userId, userMessage, botResponse)).resolves.not.toThrow();
        });
    });
    describe('getMemoryHistory', () => {
        it('should retrieve memory history for a session', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            const limit = 5;
            const mockHistory = [
                {
                    id: 1,
                    userMessage: 'Bonjour',
                    botResponse: 'Bonjour !',
                    context: null,
                    createdAt: new Date('2025-01-01T10:00:00Z'),
                },
                {
                    id: 2,
                    userMessage: 'Comment ça va ?',
                    botResponse: 'Très bien, merci !',
                    context: null,
                    createdAt: new Date('2025-01-01T10:01:00Z'),
                },
            ];
            mockPrismaService.chatMemory.findMany.mockResolvedValue(mockHistory.reverse());
            const result = await service.getMemoryHistory(sessionId, userId, limit);
            expect(mockPrismaService.chatMemory.findMany).toHaveBeenCalledWith({
                where: { sessionId, userId },
                orderBy: { createdAt: 'desc' },
                take: limit,
                select: {
                    id: true,
                    userMessage: true,
                    botResponse: true,
                    context: true,
                    createdAt: true,
                },
            });
            expect(result).toHaveLength(2);
            expect(result[0].userMessage).toBe('Bonjour');
        });
        it('should return empty array on error', async () => {
            const sessionId = 'test-session-123';
            mockPrismaService.chatMemory.findMany.mockRejectedValue(new Error('Database error'));
            const result = await service.getMemoryHistory(sessionId);
            expect(result).toEqual([]);
        });
    });
    describe('clearMemory', () => {
        it('should clear memory for a session', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            mockPrismaService.chatMemory.deleteMany.mockResolvedValue({ count: 5 });
            const result = await service.clearMemory(sessionId, userId);
            expect(mockPrismaService.chatMemory.deleteMany).toHaveBeenCalledWith({
                where: { sessionId, userId },
            });
            expect(result).toBe(true);
        });
        it('should return false on error', async () => {
            const sessionId = 'test-session-123';
            mockPrismaService.chatMemory.deleteMany.mockRejectedValue(new Error('Database error'));
            const result = await service.clearMemory(sessionId);
            expect(result).toBe(false);
        });
    });
    describe('getMemoryContext', () => {
        it('should format memory context for AI', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            const limit = 3;
            const mockHistory = [
                {
                    id: 1,
                    userMessage: 'Bonjour',
                    botResponse: 'Bonjour ! Comment puis-je vous aider ?',
                    context: null,
                    createdAt: new Date(),
                },
                {
                    id: 2,
                    userMessage: 'Combien d\'utilisateurs avez-vous ?',
                    botResponse: 'Il y a actuellement 10 utilisateurs dans la base de données.',
                    context: null,
                    createdAt: new Date(),
                },
            ];
            mockPrismaService.chatMemory.findMany.mockResolvedValue(mockHistory.reverse());
            const result = await service.getMemoryContext(sessionId, userId, limit);
            expect(result).toContain('Contexte de la conversation précédente:');
            expect(result).toContain('Utilisateur: Bonjour');
            expect(result).toContain('Assistant: Bonjour ! Comment puis-je vous aider ?');
            expect(result).toContain('Utilisateur: Combien d\'utilisateurs avez-vous ?');
        });
        it('should return empty string when no history', async () => {
            const sessionId = 'test-session-123';
            mockPrismaService.chatMemory.findMany.mockResolvedValue([]);
            const result = await service.getMemoryContext(sessionId);
            expect(result).toBe('');
        });
    });
    describe('processMessage with memory', () => {
        it('should save greeting message to memory', async () => {
            const sessionId = 'test-session-123';
            const userId = 1;
            const message = 'Bonjour';
            mockPrismaService.chatMemory.create.mockResolvedValue({});
            const result = await service.processMessage(message, sessionId, userId);
            expect(result).toContain('Bonjour ! Je suis l\'assistant');
            expect(mockPrismaService.chatMemory.create).toHaveBeenCalled();
        });
        it('should work without sessionId', async () => {
            const message = 'Bonjour';
            const result = await service.processMessage(message);
            expect(result).toContain('Bonjour ! Je suis l\'assistant');
            expect(mockPrismaService.chatMemory.create).not.toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=chatbot-memory.test.js.map