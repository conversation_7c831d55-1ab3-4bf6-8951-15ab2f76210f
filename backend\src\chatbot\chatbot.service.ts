import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import axios from 'axios';

// Configuration des entités de la base de données
const DATABASE_ENTITIES = {
  user: {
    name: 'utilisateur',
    plural: 'utilisateurs',
    keywords: ['user', 'users', 'utilisateur', 'utilisateurs', 'compte', 'email', 'profil'],
    displayField: 'name'
  },
  program: {
    name: 'programme',
    plural: 'programmes',
    keywords: ['program', 'programs', 'programme', 'programmes', 'formation'],
    displayField: 'name'
  },
  module: {
    name: 'module',
    plural: 'modules',
    keywords: ['module', 'modules', 'chapitre', 'section'],
    displayField: 'name'
  },
  course: {
    name: 'cours',
    plural: 'cours',
    keywords: ['course', 'courses', 'cours', 'leçon'],
    displayField: 'title'
  },
  contenu: {
    name: 'contenu',
    plural: 'contenus',
    keywords: ['contenu', 'contenus', 'document', 'ressource', 'fichier'],
    displayField: 'title'
  },
  quiz: {
    name: 'quiz',
    plural: 'quiz',
    keywords: ['quiz', 'quizzes', 'test', 'évaluation', 'question'],
    displayField: 'title'
  }
};

// Réponses par défaut
const RESPONSES = {
  greeting: "Bonjour ! Je suis l'assistant de la plateforme LMS MKA. Comment puis-je vous aider aujourd'hui ?",
  help: "Je peux vous fournir des informations sur :\n• Les utilisateurs et leurs rôles\n• Les programmes de formation\n• Les modules et cours\n• Les contenus et quiz\n• Les statistiques de la plateforme\n\nPosez-moi une question !",
  notUnderstood: "Je ne comprends pas votre demande. Tapez 'aide' pour voir ce que je peux faire.",
  databaseError: "Désolé, je ne peux pas accéder aux données pour le moment. La base de données n'est pas disponible.",
  noData: "Aucune donnée trouvée pour votre recherche."
};

function normalize(str: string): string {
  return str
    .replace(/[''`]/g, "'")
    .replace(/\s+/g, ' ')
    .toLowerCase()
    .trim();
}

@Injectable()
export class ChatbotService {
  private readonly logger = new Logger(ChatbotService.name);
  private readonly groqApiKey = process.env.GROQ_API_KEY;
  private readonly groqModel = 'llama-3.3-70b-versatile';

  constructor(
    private prisma: PrismaService
  ) {}

  async processMessage(message: string, sessionId?: string, userId?: number): Promise<string> {
    try {
      const normalizedMsg = normalize(message);

      // Salutations
      if (this.isGreeting(normalizedMsg)) {
        const response = RESPONSES.greeting;
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Aide
      if (this.isHelpRequest(normalizedMsg)) {
        const response = this.getEnhancedHelp();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Demandes d'information sur les fonctionnalités de fichiers
      if (this.isFileHelpRequest(normalizedMsg)) {
        const response = this.getFileProcessingHelp();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Statistiques générales
      if (this.isStatsRequest(normalizedMsg)) {
        const response = await this.getGeneralStats();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Requêtes spécifiques par entité
      const entityResponse = await this.handleEntityQueries(normalizedMsg);
      if (entityResponse) {
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, entityResponse);
        }
        return entityResponse;
      }

      // Utiliser Groq pour les autres questions avec contexte de mémoire
      if (this.groqApiKey) {
        const groqResponse = await this.askGroqWithMemory(message, sessionId, userId);
        if (groqResponse) {
          if (sessionId) {
            await this.saveToMemory(sessionId, userId, message, groqResponse);
          }
          return groqResponse;
        }
      }

      const response = RESPONSES.notUnderstood;
      if (sessionId) {
        await this.saveToMemory(sessionId, userId, message, response);
      }
      return response;
    } catch (error) {
      this.logger.error(`Erreur lors du traitement du message: ${error.message}`);
      return RESPONSES.databaseError;
    }
  }

  private isGreeting(msg: string): boolean {
    const greetings = ['hello', 'bonjour', 'salut', 'hi', 'hey', 'bonsoir'];
    return greetings.some(greeting => msg.includes(greeting));
  }

  private isHelpRequest(msg: string): boolean {
    const helpKeywords = ['aide', 'help', 'aidez-moi', 'que peux-tu faire', 'what can you do'];
    return helpKeywords.some(keyword => msg.includes(keyword));
  }

  private isFileHelpRequest(msg: string): boolean {
    const fileKeywords = ['fichier', 'file', 'document', 'résumé', 'summary', 'upload', 'télécharger', 'analyser'];
    return fileKeywords.some(keyword => msg.includes(keyword));
  }

  private getEnhancedHelp(): string {
    return RESPONSES.help + "\n\n" +
      "**Fonctionnalités disponibles:**\n" +
      "• 🧠 Mémoire de conversation intelligente\n" +
      "• � Statistiques de la plateforme\n" +
      "• 🔍 Recherche dans la base de données\n" +
      "• � Conversations contextuelles\n\n" +
      "Tapez 'aide' pour plus d'informations !";
  }

  private getFileProcessingHelp(): string {
    return "📄 **Fonctionnalités de traitement de fichiers**\n\n" +
      "Les fonctionnalités de traitement de fichiers ne sont pas encore disponibles.\n" +
      "Pour l'instant, je peux vous aider avec :\n" +
      "• Les informations sur la plateforme\n" +
      "• Les statistiques des utilisateurs\n" +
      "• Les questions générales sur l'éducation\n\n" +
      "Posez-moi une question !";
  }

  private isStatsRequest(msg: string): boolean {
    const statsKeywords = ['statistiques', 'stats', 'résumé', 'overview', 'dashboard'];
    return statsKeywords.some(keyword => msg.includes(keyword));
  }

  private async getGeneralStats(): Promise<string> {
    try {
      const [userCount, programCount, moduleCount, courseCount, contenuCount, quizCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.program.count(),
        this.prisma.module.count(),
        this.prisma.course.count(),
        this.prisma.contenu.count(),
        this.prisma.quiz.count()
      ]);

      return `📊 **Statistiques de la plateforme MKA LMS**\n\n` +
        `👥 Utilisateurs: ${userCount}\n` +
        `📚 Programmes: ${programCount}\n` +
        `📖 Modules: ${moduleCount}\n` +
        `🎓 Cours: ${courseCount}\n` +
        `📄 Contenus: ${contenuCount}\n` +
        `❓ Quiz: ${quizCount}`;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des statistiques: ${error.message}`);
      return RESPONSES.databaseError;
    }
  }

  private async handleEntityQueries(msg: string): Promise<string | null> {
    for (const [entityKey, entity] of Object.entries(DATABASE_ENTITIES)) {
      // Vérifier si le message contient des mots-clés de cette entité
      const hasKeyword = entity.keywords.some(keyword => msg.includes(keyword));
      if (!hasKeyword) continue;

      try {
        // Compter les éléments
        if (this.isCountQuery(msg)) {
          const count = await this.prisma[entityKey].count();
          return `Il y a actuellement **${count}** ${entity.plural} dans la base de données.`;
        }

        // Lister les éléments
        if (this.isListQuery(msg)) {
          const items = await this.prisma[entityKey].findMany({
            take: 10,
            select: {
              id: true,
              [entity.displayField]: true
            }
          });

          if (items.length === 0) {
            return `Aucun ${entity.name} trouvé dans la base de données.`;
          }

          const itemList = items
            .map((item: any) => `• ${item[entity.displayField] || `ID: ${item.id}`}`)
            .join('\n');

          return `📋 **Liste des ${entity.plural}:**\n\n${itemList}`;
        }

        // Détails d'un élément
        if (this.isDetailQuery(msg)) {
          const item = await this.prisma[entityKey].findFirst();
          if (!item) {
            return `Aucun ${entity.name} trouvé dans la base de données.`;
          }

          return `🔍 **Détail d'un ${entity.name}:**\n\`\`\`json\n${JSON.stringify(item, null, 2)}\n\`\`\``;
        }
      } catch (error) {
        this.logger.error(`Erreur lors de la requête ${entityKey}: ${error.message}`);
        return `Désolé, je ne peux pas accéder aux informations sur les ${entity.plural} pour le moment.`;
      }
    }

    return null;
  }

  private isCountQuery(msg: string): boolean {
    const countKeywords = ['combien', 'nombre', 'how many', 'count'];
    return countKeywords.some(keyword => msg.includes(keyword));
  }

  private isListQuery(msg: string): boolean {
    const listKeywords = ['liste', 'list', 'affiche', 'montre', 'show'];
    return listKeywords.some(keyword => msg.includes(keyword));
  }

  private isDetailQuery(msg: string): boolean {
    const detailKeywords = ['détail', 'detail', 'info', 'information'];
    return detailKeywords.some(keyword => msg.includes(keyword));
  }

  private async askGroq(message: string): Promise<string | null> {
    try {
      if (!this.groqApiKey) {
        this.logger.warn('Clé API Groq non configurée');
        return null;
      }

      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            {
              role: 'system',
              content: 'Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Réponds de façon concise et précise en français. Tu peux aider avec des questions sur l\'éducation, la formation, et la gestion de contenu pédagogique.'
            },
            { role: 'user', content: message }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || null;
    } catch (error) {
      this.logger.error(`Erreur lors de l'appel à Groq: ${error.message}`);
      if (error.response) {
        this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
      }
      return null;
    }
  }

  private async askGroqWithMemory(message: string, sessionId?: string, userId?: number): Promise<string | null> {
    try {
      if (!this.groqApiKey) {
        this.logger.warn('Clé API Groq non configurée');
        return null;
      }

      // Récupérer le contexte de la conversation si sessionId est fourni
      let contextMessage = '';
      if (sessionId) {
        contextMessage = await this.getMemoryContext(sessionId, userId, 3);
      }

      const systemContent = 'Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Réponds de façon concise et précise en français. Tu peux aider avec des questions sur l\'éducation, la formation, et la gestion de contenu pédagogique.' +
        (contextMessage ? '\n\nUtilise le contexte suivant pour maintenir la cohérence de la conversation:\n' + contextMessage : '');

      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            {
              role: 'system',
              content: systemContent
            },
            { role: 'user', content: message }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || null;
    } catch (error) {
      this.logger.error(`Erreur lors de l'appel à Groq avec mémoire: ${error.message}`);
      if (error.response) {
        this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
      }
      return null;
    }
  }

  async executeQuery(query: string): Promise<any> {
    try {
      if (!query.toLowerCase().trim().startsWith('select')) {
        throw new Error('Seules les requêtes SELECT sont autorisées');
      }
      const results = await this.prisma.$queryRawUnsafe(query);
      return results;
    } catch (error) {
      this.logger.error(`Erreur SQL: ${error.message}`);
      return { error: error.message };
    }
  }

  // ===== MÉTHODES DE GESTION DE LA MÉMOIRE =====

  async saveToMemory(sessionId: string, userId: number | undefined, userMessage: string, botResponse: string, context?: string): Promise<void> {
    try {
      this.logger.log(`🔄 Tentative de sauvegarde - Session: ${sessionId}, User: ${userId}, Message: ${userMessage.substring(0, 50)}...`);

      // Test de connexion à la base de données
      this.logger.log(`🔍 Test de connexion à la base de données...`);
      const testConnection = await this.prisma.$queryRaw`SELECT 1 as test`;
      this.logger.log(`✅ Connexion DB OK: ${JSON.stringify(testConnection)}`);

      // Test d'existence de la table ChatMemory
      const tableExists = await this.prisma.$queryRaw`SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'ChatMemory')`;
      this.logger.log(`🔍 Table ChatMemory existe: ${JSON.stringify(tableExists)}`);

      const result = await this.prisma.chatMemory.create({
        data: {
          sessionId,
          userId,
          userMessage,
          botResponse,
          context
        }
      });

      this.logger.log(`✅ Conversation sauvegardée avec succès - ID: ${result.id}, Session: ${sessionId}`);
    } catch (error) {
      this.logger.error(`❌ Erreur lors de la sauvegarde en mémoire: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
    }
  }

  async getMemoryHistory(sessionId: string, userId?: number, limit: number = 10): Promise<any[]> {
    try {
      const whereClause: any = { sessionId };
      if (userId) {
        whereClause.userId = userId;
      }

      const history = await this.prisma.chatMemory.findMany({
        where: whereClause,
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          userMessage: true,
          botResponse: true,
          context: true,
          createdAt: true
        }
      });

      return history.reverse(); // Retourner dans l'ordre chronologique
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de l'historique: ${error.message}`);
      return [];
    }
  }

  async clearMemory(sessionId: string, userId?: number): Promise<boolean> {
    try {
      const whereClause: any = { sessionId };
      if (userId) {
        whereClause.userId = userId;
      }

      await this.prisma.chatMemory.deleteMany({
        where: whereClause
      });

      this.logger.log(`Mémoire effacée pour la session: ${sessionId}`);
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de l'effacement de la mémoire: ${error.message}`);
      return false;
    }
  }

  async getMemoryContext(sessionId: string, userId?: number, limit: number = 5): Promise<string> {
    try {
      const history = await this.getMemoryHistory(sessionId, userId, limit);

      if (history.length === 0) {
        return '';
      }

      const context = history.map(entry =>
        `Utilisateur: ${entry.userMessage}\nAssistant: ${entry.botResponse}`
      ).join('\n\n');

      return `Contexte de la conversation précédente:\n${context}\n\n`;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du contexte: ${error.message}`);
      return '';
    }
  }
}
