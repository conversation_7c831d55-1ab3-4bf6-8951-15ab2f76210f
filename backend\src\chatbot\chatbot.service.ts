import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'nestjs-prisma';
import axios from 'axios';

// Configuration des entités de la base de données
const DATABASE_ENTITIES = {
  user: {
    name: 'utilisateur',
    plural: 'utilisateurs',
    keywords: ['user', 'users', 'utilisateur', 'utilisateurs', 'compte', 'email', 'profil', 'étudiant', 'formateur', 'admin'],
    displayField: 'name',
    include: {
      formateurs: true,
      Etudiants: true,
      Admins: true,
      sentFeedback: true,
      receivedFeedback: true
    }
  },
  program: {
    name: 'programme',
    plural: 'programmes',
    keywords: ['program', 'programs', 'programme', 'programmes', 'formation', 'cursus'],
    displayField: 'name',
    include: {
      modules: {
        include: {
          module: {
            include: {
              courses: {
                include: {
                  course: {
                    include: {
                      courseContenus: {
                        include: {
                          contenu: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  module: {
    name: 'module',
    plural: 'modules',
    keywords: ['module', 'modules', 'chapitre', 'section', 'unité'],
    displayField: 'name',
    include: {
      courses: {
        include: {
          course: {
            include: {
              courseContenus: {
                include: {
                  contenu: true
                }
              }
            }
          }
        }
      }
    }
  },
  course: {
    name: 'cours',
    plural: 'cours',
    keywords: ['course', 'courses', 'cours', 'leçon', 'matière'],
    displayField: 'title',
    include: {
      courseContenus: {
        include: {
          contenu: {
            include: {
              quiz: {
                include: {
                  questions: true
                }
              }
            }
          }
        }
      }
    }
  },
  contenu: {
    name: 'contenu',
    plural: 'contenus',
    keywords: ['contenu', 'contenus', 'document', 'ressource', 'fichier', 'pdf', 'video', 'image'],
    displayField: 'title',
    include: {
      quiz: {
        include: {
          questions: {
            include: {
              answers: true
            }
          }
        }
      }
    }
  },
  quiz: {
    name: 'quiz',
    plural: 'quiz',
    keywords: ['quiz', 'quizzes', 'test', 'évaluation', 'question', 'examen'],
    displayField: 'title',
    include: {
      questions: {
        include: {
          answers: true
        }
      },
      contenu: true
    }
  },
  feedback: {
    name: 'feedback',
    plural: 'feedbacks',
    keywords: ['feedback', 'avis', 'commentaire', 'retour', 'évaluation'],
    displayField: 'content',
    include: {
      sender: true,
      receiver: true,
      responses: true
    }
  }
};

// Réponses par défaut
const RESPONSES = {
  greeting: "Bonjour ! Je suis l'assistant de la plateforme LMS MKA. Comment puis-je vous aider aujourd'hui ?",
  help: "Je peux vous fournir des informations sur :\n• Les utilisateurs et leurs rôles\n• Les programmes de formation\n• Les modules et cours\n• Les contenus et quiz\n• Les statistiques de la plateforme\n\nPosez-moi une question !",
  notUnderstood: "Je ne comprends pas votre demande. Tapez 'aide' pour voir ce que je peux faire.",
  databaseError: "Désolé, je ne peux pas accéder aux données pour le moment. La base de données n'est pas disponible.",
  noData: "Aucune donnée trouvée pour votre recherche."
};

function normalize(str: string): string {
  return str
    .replace(/[''`]/g, "'")
    .replace(/\s+/g, ' ')
    .toLowerCase()
    .trim();
}

@Injectable()
export class ChatbotService {
  private readonly logger = new Logger(ChatbotService.name);
  private readonly groqApiKey = process.env.GROQ_API_KEY;
  private readonly groqModel = 'llama-3.3-70b-versatile';

  constructor(
    private prisma: PrismaService
  ) {}

  async processMessage(message: string, sessionId?: string, userId?: number): Promise<string> {
    try {
      this.logger.log(`🚀 Processing message: "${message}" with sessionId: ${sessionId}, userId: ${userId}`);
      const normalizedMsg = normalize(message);

      // Salutations
      if (this.isGreeting(normalizedMsg)) {
        let response = RESPONSES.greeting;
        this.logger.log(`✅ Greeting detected, response: ${response.substring(0, 50)}...`);
        if (sessionId) {
          this.logger.log(`💾 Calling saveToMemory for greeting...`);
          try {
            await this.saveToMemory(sessionId, userId, message, response);
            response += " [MEMORY: SAVED]";
          } catch (error) {
            response += ` [MEMORY: ERROR - ${error.message}]`;
          }
        } else {
          response += " [MEMORY: NO SESSION]";
        }
        return response;
      }

      // Aide
      if (this.isHelpRequest(normalizedMsg)) {
        const response = this.getEnhancedHelp();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Demandes d'information sur les fonctionnalités de fichiers
      if (this.isFileHelpRequest(normalizedMsg)) {
        const response = this.getFileProcessingHelp();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Statistiques générales
      if (this.isStatsRequest(normalizedMsg)) {
        const response = await this.getGeneralStats();
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, response);
        }
        return response;
      }

      // Requêtes spécifiques par entité
      const entityResponse = await this.handleEntityQueries(normalizedMsg);
      if (entityResponse) {
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, entityResponse);
        }
        return entityResponse;
      }

      // Requêtes complexes et analytiques
      const complexResponse = await this.handleComplexQueries(normalizedMsg);
      if (complexResponse) {
        if (sessionId) {
          await this.saveToMemory(sessionId, userId, message, complexResponse);
        }
        return complexResponse;
      }

      // Utiliser Groq pour les autres questions avec contexte de mémoire
      if (this.groqApiKey) {
        const groqResponse = await this.askGroqWithMemory(message, sessionId, userId);
        if (groqResponse) {
          if (sessionId) {
            await this.saveToMemory(sessionId, userId, message, groqResponse);
          }
          return groqResponse;
        }
      }

      const response = RESPONSES.notUnderstood;
      if (sessionId) {
        await this.saveToMemory(sessionId, userId, message, response);
      }
      return response;
    } catch (error) {
      this.logger.error(`Erreur lors du traitement du message: ${error.message}`);
      return RESPONSES.databaseError;
    }
  }

  private isGreeting(msg: string): boolean {
    const greetings = ['hello', 'bonjour', 'salut', 'hi', 'hey', 'bonsoir'];
    return greetings.some(greeting => msg.includes(greeting));
  }

  private isHelpRequest(msg: string): boolean {
    const helpKeywords = ['aide', 'help', 'aidez-moi', 'que peux-tu faire', 'what can you do'];
    return helpKeywords.some(keyword => msg.includes(keyword));
  }

  private isFileHelpRequest(msg: string): boolean {
    const fileKeywords = ['fichier', 'file', 'document', 'résumé', 'summary', 'upload', 'télécharger', 'analyser'];
    return fileKeywords.some(keyword => msg.includes(keyword));
  }

  private getEnhancedHelp(): string {
    return RESPONSES.help + "\n\n" +
      "**Fonctionnalités disponibles:**\n" +
      "• 🧠 Mémoire de conversation intelligente\n" +
      "• � Statistiques de la plateforme\n" +
      "• 🔍 Recherche dans la base de données\n" +
      "• � Conversations contextuelles\n\n" +
      "Tapez 'aide' pour plus d'informations !";
  }

  private getFileProcessingHelp(): string {
    return "📄 **Fonctionnalités de traitement de fichiers**\n\n" +
      "Les fonctionnalités de traitement de fichiers ne sont pas encore disponibles.\n" +
      "Pour l'instant, je peux vous aider avec :\n" +
      "• Les informations sur la plateforme\n" +
      "• Les statistiques des utilisateurs\n" +
      "• Les questions générales sur l'éducation\n\n" +
      "Posez-moi une question !";
  }

  private isStatsRequest(msg: string): boolean {
    const statsKeywords = ['statistiques', 'stats', 'résumé', 'overview', 'dashboard'];
    return statsKeywords.some(keyword => msg.includes(keyword));
  }

  private async getGeneralStats(): Promise<string> {
    try {
      const [userCount, programCount, moduleCount, courseCount, contenuCount, quizCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.program.count(),
        this.prisma.module.count(),
        this.prisma.course.count(),
        this.prisma.contenu.count(),
        this.prisma.quiz.count()
      ]);

      return `📊 **Statistiques de la plateforme MKA LMS**\n\n` +
        `👥 Utilisateurs: ${userCount}\n` +
        `📚 Programmes: ${programCount}\n` +
        `📖 Modules: ${moduleCount}\n` +
        `🎓 Cours: ${courseCount}\n` +
        `📄 Contenus: ${contenuCount}\n` +
        `❓ Quiz: ${quizCount}`;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération des statistiques: ${error.message}`);
      return RESPONSES.databaseError;
    }
  }

  private async handleEntityQueries(msg: string): Promise<string | null> {
    for (const [entityKey, entity] of Object.entries(DATABASE_ENTITIES)) {
      // Vérifier si le message contient des mots-clés de cette entité
      const hasKeyword = entity.keywords.some(keyword => msg.includes(keyword));
      if (!hasKeyword) continue;

      try {
        // Compter les éléments
        if (this.isCountQuery(msg)) {
          const count = await this.prisma[entityKey].count();
          return `Il y a actuellement **${count}** ${entity.plural} dans la base de données.`;
        }

        // Lister les éléments avec plus de détails
        if (this.isListQuery(msg)) {
          const items = await this.prisma[entityKey].findMany({
            take: 10,
            include: entity.include || {}
          });

          if (items.length === 0) {
            return `Aucun ${entity.name} trouvé dans la base de données.`;
          }

          return this.formatEntityList(items, entity, entityKey);
        }

        // Détails d'un élément avec relations
        if (this.isDetailQuery(msg)) {
          const item = await this.prisma[entityKey].findFirst({
            include: entity.include || {}
          });
          if (!item) {
            return `Aucun ${entity.name} trouvé dans la base de données.`;
          }

          return this.formatEntityDetail(item, entity, entityKey);
        }

        // Recherche par nom/titre
        if (this.isSearchQuery(msg)) {
          const searchTerm = this.extractSearchTerm(msg);
          if (searchTerm) {
            const items = await this.searchEntity(entityKey, entity, searchTerm);
            if (items.length === 0) {
              return `Aucun ${entity.name} trouvé avec le terme "${searchTerm}".`;
            }
            return this.formatSearchResults(items, entity, searchTerm);
          }
        }
      } catch (error) {
        this.logger.error(`Erreur lors de la requête ${entityKey}: ${error.message}`);
        return `Désolé, je ne peux pas accéder aux informations sur les ${entity.plural} pour le moment.`;
      }
    }

    return null;
  }

  private async handleComplexQueries(msg: string): Promise<string | null> {
    try {
      // Statistiques générales
      if (msg.includes('statistique') || msg.includes('stats') || msg.includes('résumé')) {
        return await this.getAdvancedStats();
      }

      // Questions sur les relations
      if (msg.includes('qui enseigne') || msg.includes('formateur')) {
        return await this.getInstructorInfo(msg);
      }

      // Questions sur les programmes populaires
      if (msg.includes('populaire') || msg.includes('plus suivi')) {
        return await this.getPopularPrograms();
      }

      // Questions sur les quiz et évaluations
      if (msg.includes('quiz difficile') || msg.includes('évaluation')) {
        return await this.getQuizStats();
      }

      // Questions sur l'activité récente
      if (msg.includes('récent') || msg.includes('nouveau') || msg.includes('dernière')) {
        return await this.getRecentActivity();
      }

    } catch (error) {
      this.logger.error(`Erreur dans handleComplexQueries: ${error.message}`);
    }

    return null;
  }

  private isCountQuery(msg: string): boolean {
    const countKeywords = ['combien', 'nombre', 'how many', 'count'];
    return countKeywords.some(keyword => msg.includes(keyword));
  }

  private isListQuery(msg: string): boolean {
    const listKeywords = ['liste', 'list', 'affiche', 'montre', 'show'];
    return listKeywords.some(keyword => msg.includes(keyword));
  }

  private isDetailQuery(msg: string): boolean {
    const detailKeywords = ['détail', 'detail', 'info', 'information'];
    return detailKeywords.some(keyword => msg.includes(keyword));
  }

  private async askGroq(message: string): Promise<string | null> {
    try {
      if (!this.groqApiKey) {
        this.logger.warn('Clé API Groq non configurée');
        return null;
      }

      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            {
              role: 'system',
              content: 'Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Réponds de façon concise et précise en français. Tu peux aider avec des questions sur l\'éducation, la formation, et la gestion de contenu pédagogique.'
            },
            { role: 'user', content: message }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || null;
    } catch (error) {
      this.logger.error(`Erreur lors de l'appel à Groq: ${error.message}`);
      if (error.response) {
        this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
      }
      return null;
    }
  }

  private async askGroqWithMemory(message: string, sessionId?: string, userId?: number): Promise<string | null> {
    try {
      if (!this.groqApiKey) {
        this.logger.warn('Clé API Groq non configurée');
        return null;
      }

      // Récupérer le contexte de la conversation si sessionId est fourni
      let contextMessage = '';
      if (sessionId) {
        contextMessage = await this.getMemoryContext(sessionId, userId, 3);
        this.logger.log(`🧠 Context retrieved for session ${sessionId}: "${contextMessage.substring(0, 100)}..."`);
      }

      const systemContent = `Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Tu as accès à toute la base de données et peux répondre aux questions sur:

📚 **DONNÉES DISPONIBLES:**
• **Utilisateurs** - Étudiants, Formateurs, Admins, Créateurs de formation
• **Programmes** - Formations complètes avec modules et cours
• **Modules** - Sections de formation avec cours associés
• **Cours** - Leçons individuelles avec contenus
• **Contenus** - PDFs, vidéos, images, quiz, exercices
• **Quiz** - Tests avec questions et réponses
• **Feedback** - Avis et commentaires entre utilisateurs
• **Sessions** - Sessions de formation en cours

🔍 **CAPACITÉS:**
• Compter les éléments ("combien d'utilisateurs")
• Lister les éléments ("liste des programmes")
• Chercher par nom ("trouve le cours JavaScript")
• Afficher les détails ("détails du programme")
• Analyser les relations entre entités

Réponds de façon concise et précise en français. Utilise les données réelles de la base de données pour tes réponses.` +
        (contextMessage ? '\n\n**CONTEXTE DE LA CONVERSATION:**\n' + contextMessage : '');

      this.logger.log(`🤖 Sending to Groq with system content length: ${systemContent.length}`);

      const response = await axios.post(
        'https://api.groq.com/openai/v1/chat/completions',
        {
          model: this.groqModel,
          messages: [
            {
              role: 'system',
              content: systemContent
            },
            { role: 'user', content: message }
          ],
          temperature: 0.7,
          max_tokens: 1000,
        },
        {
          headers: {
            Authorization: `Bearer ${this.groqApiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 15000,
        }
      );

      return response.data?.choices?.[0]?.message?.content || null;
    } catch (error) {
      this.logger.error(`Erreur lors de l'appel à Groq avec mémoire: ${error.message}`);
      if (error.response) {
        this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
      }
      return null;
    }
  }

  async executeQuery(query: string): Promise<any> {
    try {
      if (!query.toLowerCase().trim().startsWith('select')) {
        throw new Error('Seules les requêtes SELECT sont autorisées');
      }
      const results = await this.prisma.$queryRawUnsafe(query);
      return results;
    } catch (error) {
      this.logger.error(`Erreur SQL: ${error.message}`);
      return { error: error.message };
    }
  }

  // ===== MÉTHODES DE GESTION DE LA MÉMOIRE =====

  async saveToMemory(sessionId: string, userId: number | undefined, userMessage: string, botResponse: string, context?: string): Promise<void> {
    try {
      // Vérifier si l'utilisateur existe avant de l'associer
      let validUserId: number | null = null;
      if (userId) {
        // S'assurer que userId est un nombre entier
        const numericUserId = typeof userId === 'string' ? parseInt(userId, 10) : userId;
        if (!isNaN(numericUserId)) {
          const userExists = await this.prisma.user.findUnique({
            where: { id: numericUserId }
          });
          if (userExists) {
            validUserId = numericUserId;
          } else {
            this.logger.warn(`👤 Utilisateur avec ID ${numericUserId} non trouvé, sauvegarde sans association utilisateur`);
          }
        } else {
          this.logger.warn(`👤 ID utilisateur invalide: ${userId}, sauvegarde sans association utilisateur`);
        }
      }

      const result = await this.prisma.chatMemory.create({
        data: {
          sessionId,
          userId: validUserId,
          userMessage,
          botResponse,
          context
        }
      });

      this.logger.log(`✅ Conversation sauvegardée avec succès - ID: ${result.id}, Session: ${sessionId}, User: ${validUserId}`);
    } catch (error) {
      this.logger.error(`❌ Erreur lors de la sauvegarde en mémoire: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      throw error; // Re-throw to see the error in the response
    }
  }

  async getMemoryHistory(sessionId: string, userId?: number, limit: number = 10): Promise<any[]> {
    try {
      // Pour la recherche, on cherche d'abord par sessionId seulement
      // car l'utilisateur peut ne pas exister dans la base de données
      const history = await this.prisma.chatMemory.findMany({
        where: { sessionId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          userMessage: true,
          botResponse: true,
          context: true,
          createdAt: true,
          userId: true
        }
      });

      return history.reverse(); // Retourner dans l'ordre chronologique
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération de l'historique: ${error.message}`);
      return [];
    }
  }

  async clearMemory(sessionId: string, userId?: number): Promise<boolean> {
    try {
      const whereClause: any = { sessionId };
      if (userId) {
        whereClause.userId = userId;
      }

      await this.prisma.chatMemory.deleteMany({
        where: whereClause
      });

      this.logger.log(`Mémoire effacée pour la session: ${sessionId}`);
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de l'effacement de la mémoire: ${error.message}`);
      return false;
    }
  }

  async getMemoryContext(sessionId: string, userId?: number, limit: number = 5): Promise<string> {
    try {
      const history = await this.getMemoryHistory(sessionId, userId, limit);

      if (history.length === 0) {
        return '';
      }

      const context = history.map(entry =>
        `Utilisateur: ${entry.userMessage}\nAssistant: ${entry.botResponse}`
      ).join('\n\n');

      return `Contexte de la conversation précédente:\n${context}\n\n`;
    } catch (error) {
      this.logger.error(`Erreur lors de la récupération du contexte: ${error.message}`);
      return '';
    }
  }

  async testDatabaseConnection(): Promise<any> {
    try {
      // Test de connexion basique
      const connectionTest = await this.prisma.$queryRaw`SELECT 1 as test`;

      // Test de la table ChatMemory
      const tableTest = await this.prisma.chatMemory.count();

      // Test d'insertion simple
      const insertTest = await this.prisma.chatMemory.create({
        data: {
          sessionId: 'test-session',
          userId: null,
          userMessage: 'Test message',
          botResponse: 'Test response',
          context: 'Test context'
        }
      });

      // Nettoyer le test
      await this.prisma.chatMemory.delete({
        where: { id: insertTest.id }
      });

      return {
        connectionTest,
        tableTest,
        insertTest: 'SUCCESS',
        message: 'Tous les tests de base de données ont réussi'
      };
    } catch (error) {
      throw new Error(`Test de base de données échoué: ${error.message}`);
    }
  }

  async getUsers(): Promise<any[]> {
    try {
      const users = await this.prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          role: true
        }
      });
      return users;
    } catch (error) {
      throw new Error(`Erreur lors de la récupération des utilisateurs: ${error.message}`);
    }
  }

  // ===== MÉTHODES AVANCÉES DE FORMATAGE ET RECHERCHE =====

  private formatEntityList(items: any[], entity: any, entityKey: string): string {
    const itemList = items.map((item: any) => {
      let line = `• **${item[entity.displayField] || `ID: ${item.id}`}**`;

      // Ajouter des informations contextuelles selon le type d'entité
      if (entityKey === 'user') {
        line += ` (${item.role || 'Rôle non défini'})`;
        if (item.email) line += ` - ${item.email}`;
      } else if (entityKey === 'program') {
        line += ` - ${item.published ? '✅ Publié' : '❌ Non publié'}`;
        if (item.modules?.length) line += ` - ${item.modules.length} modules`;
      } else if (entityKey === 'contenu') {
        line += ` - Type: ${item.type || 'Non défini'}`;
        if (item.fileType) line += ` (${item.fileType})`;
        line += ` - ${item.published ? '✅ Publié' : '❌ Non publié'}`;
      } else if (entityKey === 'quiz') {
        if (item.questions?.length) line += ` - ${item.questions.length} questions`;
        if (item.timeLimit) line += ` - ${Math.floor(item.timeLimit / 60)} min`;
      }

      return line;
    }).join('\n');

    return `📋 **Liste des ${entity.plural} (${items.length} résultats):**\n\n${itemList}`;
  }

  private formatEntityDetail(item: any, entity: any, entityKey: string): string {
    let details = `🔍 **Détails du ${entity.name}:**\n\n`;

    // Informations de base
    details += `**Nom/Titre:** ${item[entity.displayField] || 'Non défini'}\n`;
    details += `**ID:** ${item.id}\n`;

    // Informations spécifiques selon le type
    if (entityKey === 'user') {
      details += `**Email:** ${item.email}\n`;
      details += `**Rôle:** ${item.role}\n`;
      details += `**Actif:** ${item.isActive ? 'Oui' : 'Non'}\n`;
      if (item.phone) details += `**Téléphone:** ${item.phone}\n`;
      if (item.location) details += `**Localisation:** ${item.location}\n`;
      if (item.skills?.length) details += `**Compétences:** ${item.skills.join(', ')}\n`;
    } else if (entityKey === 'program') {
      details += `**Publié:** ${item.published ? 'Oui' : 'Non'}\n`;
      if (item.modules?.length) {
        details += `**Modules (${item.modules.length}):**\n`;
        item.modules.forEach((pm: any) => {
          details += `  • ${pm.module?.name || 'Module sans nom'}\n`;
        });
      }
    } else if (entityKey === 'contenu') {
      details += `**Type:** ${item.type}\n`;
      if (item.fileType) details += `**Type de fichier:** ${item.fileType}\n`;
      details += `**Publié:** ${item.published ? 'Oui' : 'Non'}\n`;
      if (item.fileUrl) details += `**URL:** ${item.fileUrl}\n`;
      if (item.quiz) {
        details += `**Quiz associé:** ${item.quiz.title || 'Quiz sans titre'}\n`;
        if (item.quiz.questions?.length) {
          details += `**Nombre de questions:** ${item.quiz.questions.length}\n`;
        }
      }
    }

    return details;
  }

  private isSearchQuery(msg: string): boolean {
    const searchKeywords = ['cherche', 'trouve', 'search', 'find', 'recherche'];
    return searchKeywords.some(keyword => msg.includes(keyword));
  }

  private extractSearchTerm(msg: string): string | null {
    // Extraire le terme de recherche après des mots-clés comme "cherche", "trouve", etc.
    const patterns = [
      /(?:cherche|trouve|search|find|recherche)\s+(.+)/i,
      /"([^"]+)"/,  // Termes entre guillemets
      /'([^']+)'/   // Termes entre apostrophes
    ];

    for (const pattern of patterns) {
      const match = msg.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  private async searchEntity(entityKey: string, entity: any, searchTerm: string): Promise<any[]> {
    const searchField = entity.displayField;

    try {
      return await this.prisma[entityKey].findMany({
        where: {
          [searchField]: {
            contains: searchTerm,
            mode: 'insensitive'
          }
        },
        include: entity.include || {},
        take: 5
      });
    } catch (error) {
      this.logger.error(`Erreur lors de la recherche dans ${entityKey}: ${error.message}`);
      return [];
    }
  }

  private formatSearchResults(items: any[], entity: any, searchTerm: string): string {
    const results = items.map((item: any) =>
      `• **${item[entity.displayField]}** (ID: ${item.id})`
    ).join('\n');

    return `🔍 **Résultats de recherche pour "${searchTerm}" dans ${entity.plural}:**\n\n${results}`;
  }

  // ===== MÉTHODES DE REQUÊTES COMPLEXES =====

  private async getAdvancedStats(): Promise<string> {
    try {
      const [userCount, programCount, moduleCount, courseCount, contenuCount, quizCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.program.count(),
        this.prisma.module.count(),
        this.prisma.course.count(),
        this.prisma.contenu.count(),
        this.prisma.quiz.count()
      ]);

      const [publishedPrograms, publishedContent] = await Promise.all([
        this.prisma.program.count({ where: { published: true } }),
        this.prisma.contenu.count({ where: { published: true } })
      ]);

      const usersByRole = await this.prisma.user.groupBy({
        by: ['role'],
        _count: { role: true }
      });

      let stats = `📊 **Statistiques de la plateforme MKA:**\n\n`;
      stats += `👥 **Utilisateurs:** ${userCount} total\n`;

      usersByRole.forEach(group => {
        stats += `   • ${group.role}: ${group._count.role}\n`;
      });

      stats += `\n📚 **Contenu pédagogique:**\n`;
      stats += `   • Programmes: ${programCount} (${publishedPrograms} publiés)\n`;
      stats += `   • Modules: ${moduleCount}\n`;
      stats += `   • Cours: ${courseCount}\n`;
      stats += `   • Contenus: ${contenuCount} (${publishedContent} publiés)\n`;
      stats += `   • Quiz: ${quizCount}\n`;

      return stats;
    } catch (error) {
      this.logger.error(`Erreur dans getAdvancedStats: ${error.message}`);
      return "Désolé, je ne peux pas récupérer les statistiques pour le moment.";
    }
  }

  private async getInstructorInfo(msg: string): Promise<string> {
    try {
      const instructors = await this.prisma.user.findMany({
        where: { role: 'Formateur' },
        include: {
          formateurs: true
        },
        take: 5
      });

      if (instructors.length === 0) {
        return "Aucun formateur trouvé dans la base de données.";
      }

      let info = `👨‍🏫 **Formateurs de la plateforme:**\n\n`;
      instructors.forEach(instructor => {
        info += `• **${instructor.name || instructor.email}**\n`;
        if (instructor.formateurs[0]?.speciality) {
          info += `  Spécialité: ${instructor.formateurs[0].speciality}\n`;
        }
        if (instructor.email) info += `  Email: ${instructor.email}\n`;
        info += `\n`;
      });

      return info;
    } catch (error) {
      this.logger.error(`Erreur dans getInstructorInfo: ${error.message}`);
      return "Désolé, je ne peux pas récupérer les informations des formateurs.";
    }
  }

  private async getPopularPrograms(): Promise<string> {
    try {
      const programs = await this.prisma.program.findMany({
        where: { published: true },
        include: {
          modules: {
            include: {
              module: {
                include: {
                  courses: {
                    include: {
                      course: true
                    }
                  }
                }
              }
            }
          }
        },
        take: 5
      });

      if (programs.length === 0) {
        return "Aucun programme publié trouvé.";
      }

      let info = `🏆 **Programmes de formation disponibles:**\n\n`;
      programs.forEach(program => {
        const moduleCount = program.modules.length;
        const courseCount = program.modules.reduce((total, pm) => total + pm.module.courses.length, 0);

        info += `• **${program.name}**\n`;
        info += `  📚 ${moduleCount} modules, ${courseCount} cours\n`;
        info += `  ✅ Publié\n\n`;
      });

      return info;
    } catch (error) {
      this.logger.error(`Erreur dans getPopularPrograms: ${error.message}`);
      return "Désolé, je ne peux pas récupérer les informations des programmes.";
    }
  }

  private async getQuizStats(): Promise<string> {
    try {
      const quizzes = await this.prisma.quiz.findMany({
        include: {
          questions: true,
          contenu: true
        },
        take: 5
      });

      if (quizzes.length === 0) {
        return "Aucun quiz trouvé dans la base de données.";
      }

      let info = `🧠 **Quiz disponibles:**\n\n`;
      quizzes.forEach(quiz => {
        info += `• **${quiz.title || 'Quiz sans titre'}**\n`;
        info += `  📝 ${quiz.questions.length} questions\n`;
        if (quiz.timeLimit) {
          info += `  ⏱️ Durée: ${Math.floor(quiz.timeLimit / 60)} minutes\n`;
        }
        if (quiz.contenu) {
          info += `  📄 Contenu: ${quiz.contenu.title}\n`;
        }
        info += `\n`;
      });

      return info;
    } catch (error) {
      this.logger.error(`Erreur dans getQuizStats: ${error.message}`);
      return "Désolé, je ne peux pas récupérer les informations des quiz.";
    }
  }

  private async getRecentActivity(): Promise<string> {
    try {
      const recentUsers = await this.prisma.user.findMany({
        orderBy: { createdAt: 'desc' },
        take: 3,
        select: {
          name: true,
          email: true,
          role: true,
          createdAt: true
        }
      });

      const recentPrograms = await this.prisma.program.findMany({
        orderBy: { id: 'desc' },
        take: 3,
        select: {
          name: true,
          published: true
        }
      });

      let info = `🕒 **Activité récente:**\n\n`;

      if (recentUsers.length > 0) {
        info += `👥 **Nouveaux utilisateurs:**\n`;
        recentUsers.forEach(user => {
          const date = new Date(user.createdAt).toLocaleDateString('fr-FR');
          info += `• ${user.name || user.email} (${user.role}) - ${date}\n`;
        });
        info += `\n`;
      }

      if (recentPrograms.length > 0) {
        info += `📚 **Programmes récents:**\n`;
        recentPrograms.forEach(program => {
          info += `• ${program.name} ${program.published ? '✅' : '❌'}\n`;
        });
      }

      return info;
    } catch (error) {
      this.logger.error(`Erreur dans getRecentActivity: ${error.message}`);
      return "Désolé, je ne peux pas récupérer l'activité récente.";
    }
  }
}
