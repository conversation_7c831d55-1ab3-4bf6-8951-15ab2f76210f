import { ChatbotService } from './chatbot.service';
import { ChatbotMessageDto, FileProcessingDto, SummarizationRequestDto, ChatMemoryDto, ClearMemoryDto } from './dto/chatbot-message.dto';
export declare class ChatbotController {
    private readonly chatbotService;
    constructor(chatbotService: ChatbotService);
    sendMessage(dto: ChatbotMessageDto): Promise<{
        response: string;
        sessionId: string | undefined;
    }>;
    executeQuery(query: string): Promise<any>;
    uploadAndProcessFile(file: Express.Multer.File, dto: FileProcessingDto): Promise<{
        success: boolean;
        fileName: string;
        fileSize: number;
        extractedContent: {
            wordCount: number | undefined;
            pageCount: number | undefined;
            extractedAt: Date | undefined;
        };
        summary: import("./services/summarization.service").SummaryResult;
        formattedResponse: string;
    }>;
    summarizeText(dto: SummarizationRequestDto): Promise<{
        success: boolean;
        summary: import("./services/summarization.service").SummaryResult;
        quickSummary: string | undefined;
        formattedResponse: string;
    }>;
    extractFileContent(file: Express.Multer.File, dto: FileProcessingDto): Promise<{
        success: boolean;
        fileName: string;
        fileSize: number;
        extractedContent: {
            text: string;
            metadata: {
                pageCount?: number;
                wordCount?: number;
                fileSize?: number;
                extractedAt: Date;
            } | undefined;
        };
    }>;
    getSupportedFormats(): Promise<{
        supportedFormats: string[];
        maxFileSize: string;
        description: string;
    }>;
    getMemoryHistory(dto: ChatMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        history: any[];
        count: number;
    }>;
    clearMemory(dto: ClearMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        message: string;
    }>;
    getMemoryContext(dto: ChatMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        context: string;
        hasContext: boolean;
    }>;
}
