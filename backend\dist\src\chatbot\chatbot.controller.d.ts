import { ChatbotService } from './chatbot.service';
import { ChatbotMessageDto, ChatMemoryDto, ClearMemoryDto } from './dto/chatbot-message.dto';
export declare class ChatbotController {
    private readonly chatbotService;
    constructor(chatbotService: ChatbotService);
    sendMessage(dto: ChatbotMessageDto): Promise<{
        response: string;
        sessionId: string | undefined;
    }>;
    getMemoryHistory(dto: ChatMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        history: any[];
        count: number;
    }>;
    clearMemory(dto: ClearMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        message: string;
    }>;
    getMemoryContext(dto: ChatMemoryDto): Promise<{
        success: boolean;
        sessionId: string;
        context: string;
        hasContext: boolean;
    }>;
}
