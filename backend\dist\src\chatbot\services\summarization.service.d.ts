export interface SummaryResult {
    summary: string;
    keyPoints: string[];
    wordCount: {
        original: number;
        summary: number;
    };
    metadata: {
        processingTime: number;
        summarizedAt: Date;
        context?: string;
    };
}
export declare class SummarizationService {
    private readonly logger;
    private readonly groqApiKey;
    private readonly groqModel;
    summarizeText(content: string, context?: string, fileName?: string): Promise<SummaryResult>;
    private generateSummary;
    private extractKeyPoints;
    private parseKeyPoints;
    private prepareContentForSummarization;
    private countWords;
    generateQuickSummary(content: string): Promise<string>;
}
