# Exemples d'utilisation du Chatbot avec Traitement de Fichiers

## 1. Test avec un fichier texte simple

### Créer un fichier de test

```bash
echo "Ceci est un document de test pour démontrer les capacités de résumé du chatbot MKA LMS. 
Le système peut extraire le contenu de différents types de fichiers et générer des résumés intelligents.
Les fonctionnalités incluent l'extraction de points clés, l'analyse statistique et la présentation formatée des résultats.
Cette technologie utilise l'intelligence artificielle pour comprendre le contexte et fournir des résumés pertinents." > test-document.txt
```

### Upload et traitement

```bash
curl -X POST \
  http://localhost:8000/chatbot/upload-file \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test-document.txt' \
  -F 'context=Document de démonstration'
```

## 2. Test de résumé de texte direct

```bash
curl -X POST \
  http://localhost:8000/chatbot/summarize-text \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "L'\''intelligence artificielle (IA) est une technologie révolutionnaire qui transforme notre façon de travailler et de vivre. Elle permet aux machines d'\''apprendre, de raisonner et de prendre des décisions de manière autonome. Dans le domaine de l'\''éducation, l'\''IA offre des possibilités extraordinaires pour personnaliser l'\''apprentissage, automatiser les tâches administratives et améliorer l'\''engagement des étudiants. Les systèmes de gestion de l'\''apprentissage (LMS) intègrent de plus en plus ces technologies pour offrir une expérience éducative plus riche et plus efficace.",
    "context": "Article sur l'\''IA dans l'\''éducation",
    "fileName": "article-ia-education.txt"
  }'
```

## 3. Test d'extraction de contenu uniquement

```bash
curl -X POST \
  http://localhost:8000/chatbot/extract-content \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@test-document.txt'
```

## 4. Vérifier les formats supportés

```bash
curl -X POST http://localhost:8000/chatbot/supported-formats
```

## 5. Test du chat avec les nouvelles fonctionnalités

### Demander de l'aide générale

```bash
curl -X POST \
  http://localhost:8000/chatbot/message \
  -H 'Content-Type: application/json' \
  -d '{"message": "aide"}'
```

### Demander de l'aide sur les fichiers

```bash
curl -X POST \
  http://localhost:8000/chatbot/message \
  -H 'Content-Type: application/json' \
  -d '{"message": "fichier"}'
```

## 6. Test avec un fichier PDF (si disponible)

```bash
curl -X POST \
  http://localhost:8000/chatbot/upload-file \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@document.pdf' \
  -F 'context=Manuel utilisateur'
```

## Réponses attendues

### Résumé de texte - Exemple de réponse

```json
{
  "success": true,
  "summary": {
    "summary": "L'intelligence artificielle transforme l'éducation en permettant la personnalisation de l'apprentissage et l'automatisation des tâches. Les LMS intègrent ces technologies pour améliorer l'expérience éducative.",
    "keyPoints": [
      "L'IA révolutionne le travail et la vie quotidienne",
      "Personnalisation de l'apprentissage possible",
      "Automatisation des tâches administratives",
      "Amélioration de l'engagement étudiant",
      "Intégration croissante dans les LMS"
    ],
    "wordCount": {
      "original": 89,
      "summary": 25
    },
    "metadata": {
      "processingTime": 1500,
      "summarizedAt": "2025-01-01T12:00:00Z",
      "context": "Article sur l'IA dans l'éducation"
    }
  },
  "formattedResponse": "📝 **Résumé du contenu**\n\n**Résumé:**\nL'intelligence artificielle transforme l'éducation...\n\n**Points clés:**\n• L'IA révolutionne le travail et la vie quotidienne\n• Personnalisation de l'apprentissage possible\n...\n\n**Statistiques:**\n• Mots originaux: 89\n• Mots du résumé: 25\n• Réduction: 72%\n• Temps de traitement: 1500ms"
}
```

## Notes importantes

1. **Variables d'environnement** : Assurez-vous que `GROQ_API_KEY` est configurée
2. **Serveur** : Le backend doit être démarré sur le port 8000
3. **Fichiers** : Respectez la limite de 10MB
4. **Formats** : Utilisez uniquement les formats supportés (PDF, DOCX, XLSX, TXT)

## Dépannage

### Erreur "Clé API Groq non configurée"
```bash
export GROQ_API_KEY="votre_clé_api_ici"
```

### Erreur "Type de fichier non supporté"
Vérifiez que votre fichier a une extension supportée et un MIME type correct.

### Timeout lors du traitement
Les gros fichiers peuvent prendre plus de temps. Augmentez le timeout si nécessaire.
