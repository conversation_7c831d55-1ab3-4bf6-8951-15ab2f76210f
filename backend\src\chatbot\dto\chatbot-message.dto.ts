import { IsString, <PERSON>NotEmpty, <PERSON>Optional, <PERSON><PERSON>num, <PERSON>N<PERSON><PERSON> } from 'class-validator';

export class ChatbotMessageDto {
  @IsString()
  @IsNotEmpty()
  message: string;

  @IsOptional()
  @IsString()
  sessionId?: string;

  @IsOptional()
  @IsNumber()
  userId?: number;
}

export class FileProcessingDto {
  @IsOptional()
  @IsString()
  text?: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  @IsEnum(['pdf', 'docx', 'xlsx', 'txt', 'text'])
  fileType?: string;
}

export class SummarizationRequestDto {
  @IsString()
  @IsNotEmpty()
  content: string;

  @IsOptional()
  @IsString()
  context?: string;

  @IsOptional()
  @IsString()
  fileName?: string;
}

export class ChatMemoryDto {
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @IsOptional()
  @IsNumber()
  userId?: number;

  @IsOptional()
  @IsNumber()
  limit?: number;
}

export class ClearMemoryDto {
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @IsOptional()
  @IsNumber()
  userId?: number;
}