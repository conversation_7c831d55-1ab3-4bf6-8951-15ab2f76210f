import { IsString, <PERSON>NotEmpty, <PERSON>Optional, <PERSON>E<PERSON> } from 'class-validator';

export class ChatbotMessageDto {
  @IsString()
  @IsNotEmpty()
  message: string;
}

export class FileProcessingDto {
  @IsOptional()
  @IsString()
  text?: string;

  @IsOptional()
  @IsString()
  fileName?: string;

  @IsOptional()
  @IsEnum(['pdf', 'docx', 'xlsx', 'txt', 'text'])
  fileType?: string;
}

export class SummarizationRequestDto {
  @IsString()
  @IsNotEmpty()
  content: string;

  @IsOptional()
  @IsString()
  context?: string;

  @IsOptional()
  @IsString()
  fileName?: string;
}