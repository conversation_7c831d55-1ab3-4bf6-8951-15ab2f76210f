import {
  Controller,
  Post,
  Body,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ChatbotService } from './chatbot.service';
import { ChatbotMessageDto, FileProcessingDto, SummarizationRequestDto } from './dto/chatbot-message.dto';

@Controller('chatbot')
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @Post('message')
  async sendMessage(@Body() dto: ChatbotMessageDto) {
    try {
      const response = await this.chatbotService.processMessage(dto.message);
      return { response };
    } catch (error) {
      throw new HttpException(
        `Erreur lors du traitement du message: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('query')
  async executeQuery(@Body('query') query: string) {
    try {
      return await this.chatbotService.executeQuery(query);
    } catch (error) {
      throw new HttpException(
        `Erreur lors de l'exécution de la requête: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post('upload-file')
  @UseInterceptors(FileInterceptor('file', {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB max
    },
    fileFilter: (req, file, callback) => {
      const allowedMimes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/plain'
      ];

      if (allowedMimes.includes(file.mimetype)) {
        callback(null, true);
      } else {
        callback(new BadRequestException('Type de fichier non supporté'), false);
      }
    }
  }))
  async uploadAndProcessFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: FileProcessingDto
  ) {
    try {
      if (!file) {
        throw new BadRequestException('Aucun fichier fourni');
      }

      // Traiter le fichier et générer le résumé
      const result = await this.chatbotService.processFileAndSummarize(
        file.buffer,
        file.originalname,
        dto.fileName || file.originalname,
        file.mimetype
      );

      // Formater la réponse pour l'affichage
      const formattedResponse = this.chatbotService.formatSummaryResponse(
        result.summary,
        file.originalname
      );

      return {
        success: true,
        fileName: file.originalname,
        fileSize: file.size,
        extractedContent: {
          wordCount: result.extractedContent.metadata?.wordCount,
          pageCount: result.extractedContent.metadata?.pageCount,
          extractedAt: result.extractedContent.metadata?.extractedAt
        },
        summary: result.summary,
        formattedResponse
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors du traitement du fichier: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('summarize-text')
  async summarizeText(@Body() dto: SummarizationRequestDto) {
    try {
      if (!dto.content || dto.content.trim().length === 0) {
        throw new BadRequestException('Le contenu à résumer ne peut pas être vide');
      }

      const result = await this.chatbotService.processTextInput(
        dto.content,
        dto.context
      );

      // Formater la réponse pour l'affichage
      const formattedResponse = this.chatbotService.formatSummaryResponse(
        result.summary,
        dto.fileName
      );

      return {
        success: true,
        summary: result.summary,
        quickSummary: result.quickSummary,
        formattedResponse
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors de la génération du résumé: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('extract-content')
  @UseInterceptors(FileInterceptor('file', {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB max
    }
  }))
  async extractFileContent(
    @UploadedFile() file: Express.Multer.File,
    @Body() dto: FileProcessingDto
  ) {
    try {
      if (!file) {
        throw new BadRequestException('Aucun fichier fourni');
      }

      const extractedContent = await this.chatbotService.processFileContent(
        file.buffer,
        file.originalname,
        file.mimetype
      );

      return {
        success: true,
        fileName: file.originalname,
        fileSize: file.size,
        extractedContent: {
          text: extractedContent.text,
          metadata: extractedContent.metadata
        }
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors de l'extraction du contenu: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('supported-formats')
  async getSupportedFormats() {
    return {
      supportedFormats: this.chatbotService['fileExtractionService'].getSupportedFileTypes(),
      maxFileSize: '10MB',
      description: 'Formats de fichiers supportés pour l\'extraction de contenu et la génération de résumés'
    };
  }
}
