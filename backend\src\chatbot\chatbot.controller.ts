import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { ChatbotService } from './chatbot.service';
import { ChatbotMessageDto, ChatMemoryDto, ClearMemoryDto } from './dto/chatbot-message.dto';

@Controller('chatbot')
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @Post('message')
  async sendMessage(@Body() dto: ChatbotMessageDto) {
    try {
      const response = await this.chatbotService.processMessage(
        dto.message,
        dto.sessionId,
        dto.userId
      );
      return {
        response,
        sessionId: dto.sessionId
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors du traitement du message: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }





  // ===== ENDPOINTS DE GESTION DE LA MÉMOIRE =====

  @Post('memory/history')
  async getMemoryHistory(@Body() dto: ChatMemoryDto) {
    try {
      const history = await this.chatbotService.getMemoryHistory(
        dto.sessionId,
        dto.userId,
        dto.limit || 10
      );

      return {
        success: true,
        sessionId: dto.sessionId,
        history,
        count: history.length
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors de la récupération de l'historique: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('memory/clear')
  async clearMemory(@Body() dto: ClearMemoryDto) {
    try {
      const success = await this.chatbotService.clearMemory(
        dto.sessionId,
        dto.userId
      );

      return {
        success,
        sessionId: dto.sessionId,
        message: success ? 'Mémoire effacée avec succès' : 'Erreur lors de l\'effacement de la mémoire'
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors de l'effacement de la mémoire: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('memory/context')
  async getMemoryContext(@Body() dto: ChatMemoryDto) {
    try {
      const context = await this.chatbotService.getMemoryContext(
        dto.sessionId,
        dto.userId,
        dto.limit || 5
      );

      return {
        success: true,
        sessionId: dto.sessionId,
        context,
        hasContext: context.length > 0
      };
    } catch (error) {
      throw new HttpException(
        `Erreur lors de la récupération du contexte: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('debug/test-db')
  async testDatabase() {
    try {
      const result = await this.chatbotService.testDatabaseConnection();
      return {
        success: true,
        result
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  @Post('debug/users')
  async getUsers() {
    try {
      const users = await this.chatbotService.getUsers();
      return {
        success: true,
        users
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
