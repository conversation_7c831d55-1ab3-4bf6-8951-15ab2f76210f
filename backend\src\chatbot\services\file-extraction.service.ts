import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import * as pdfParse from 'pdf-parse';
import * as mammoth from 'mammoth';
import * as XLSX from 'xlsx';

export interface ExtractedContent {
  text: string;
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    fileSize?: number;
    extractedAt: Date;
  };
}

@Injectable()
export class FileExtractionService {
  private readonly logger = new Logger(FileExtractionService.name);

  async extractContent(buffer: Buffer, fileName: string, mimeType?: string): Promise<ExtractedContent> {
    try {
      const fileExtension = this.getFileExtension(fileName);
      
      switch (fileExtension.toLowerCase()) {
        case 'pdf':
          return await this.extractFromPdf(buffer);
        case 'docx':
        case 'doc':
          return await this.extractFromWord(buffer);
        case 'xlsx':
        case 'xls':
          return await this.extractFromExcel(buffer);
        case 'txt':
        case 'text':
          return this.extractFromText(buffer);
        default:
          throw new BadRequestException(`Type de fichier non supporté: ${fileExtension}`);
      }
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction du contenu: ${error.message}`);
      throw new BadRequestException(`Impossible d'extraire le contenu du fichier: ${error.message}`);
    }
  }

  private async extractFromPdf(buffer: Buffer): Promise<ExtractedContent> {
    try {
      const data = await pdfParse(buffer);
      
      return {
        text: data.text,
        metadata: {
          pageCount: data.numpages,
          wordCount: this.countWords(data.text),
          fileSize: buffer.length,
          extractedAt: new Date()
        }
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction PDF: ${error.message}`);
      throw new BadRequestException('Impossible de lire le fichier PDF');
    }
  }

  private async extractFromWord(buffer: Buffer): Promise<ExtractedContent> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      
      return {
        text: result.value,
        metadata: {
          wordCount: this.countWords(result.value),
          fileSize: buffer.length,
          extractedAt: new Date()
        }
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction Word: ${error.message}`);
      throw new BadRequestException('Impossible de lire le fichier Word');
    }
  }

  private async extractFromExcel(buffer: Buffer): Promise<ExtractedContent> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      let allText = '';
      
      workbook.SheetNames.forEach(sheetName => {
        const worksheet = workbook.Sheets[sheetName];
        const sheetData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Convertir les données en texte
        const sheetText = sheetData
          .map((row: any[]) => row.join(' | '))
          .join('\n');
        
        allText += `\n=== Feuille: ${sheetName} ===\n${sheetText}\n`;
      });
      
      return {
        text: allText.trim(),
        metadata: {
          wordCount: this.countWords(allText),
          fileSize: buffer.length,
          extractedAt: new Date()
        }
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction Excel: ${error.message}`);
      throw new BadRequestException('Impossible de lire le fichier Excel');
    }
  }

  private extractFromText(buffer: Buffer): ExtractedContent {
    try {
      const text = buffer.toString('utf-8');
      
      return {
        text,
        metadata: {
          wordCount: this.countWords(text),
          fileSize: buffer.length,
          extractedAt: new Date()
        }
      };
    } catch (error) {
      this.logger.error(`Erreur lors de l'extraction texte: ${error.message}`);
      throw new BadRequestException('Impossible de lire le fichier texte');
    }
  }

  private getFileExtension(fileName: string): string {
    const parts = fileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1] : '';
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  validateFileType(fileName: string): boolean {
    const supportedExtensions = ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'txt', 'text'];
    const extension = this.getFileExtension(fileName).toLowerCase();
    return supportedExtensions.includes(extension);
  }

  getSupportedFileTypes(): string[] {
    return ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'txt', 'text'];
  }
}
