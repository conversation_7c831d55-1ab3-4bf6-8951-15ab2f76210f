"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var FileExtractionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileExtractionService = void 0;
const common_1 = require("@nestjs/common");
const pdfParse = require("pdf-parse");
const mammoth = require("mammoth");
const XLSX = require("xlsx");
let FileExtractionService = FileExtractionService_1 = class FileExtractionService {
    logger = new common_1.Logger(FileExtractionService_1.name);
    async extractContent(buffer, fileName, mimeType) {
        try {
            const fileExtension = this.getFileExtension(fileName);
            switch (fileExtension.toLowerCase()) {
                case 'pdf':
                    return await this.extractFromPdf(buffer);
                case 'docx':
                case 'doc':
                    return await this.extractFromWord(buffer);
                case 'xlsx':
                case 'xls':
                    return await this.extractFromExcel(buffer);
                case 'txt':
                case 'text':
                    return this.extractFromText(buffer);
                default:
                    throw new common_1.BadRequestException(`Type de fichier non supporté: ${fileExtension}`);
            }
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction du contenu: ${error.message}`);
            throw new common_1.BadRequestException(`Impossible d'extraire le contenu du fichier: ${error.message}`);
        }
    }
    async extractFromPdf(buffer) {
        try {
            const data = await pdfParse(buffer);
            return {
                text: data.text,
                metadata: {
                    pageCount: data.numpages,
                    wordCount: this.countWords(data.text),
                    fileSize: buffer.length,
                    extractedAt: new Date()
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction PDF: ${error.message}`);
            throw new common_1.BadRequestException('Impossible de lire le fichier PDF');
        }
    }
    async extractFromWord(buffer) {
        try {
            const result = await mammoth.extractRawText({ buffer });
            return {
                text: result.value,
                metadata: {
                    wordCount: this.countWords(result.value),
                    fileSize: buffer.length,
                    extractedAt: new Date()
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction Word: ${error.message}`);
            throw new common_1.BadRequestException('Impossible de lire le fichier Word');
        }
    }
    async extractFromExcel(buffer) {
        try {
            const workbook = XLSX.read(buffer, { type: 'buffer' });
            let allText = '';
            workbook.SheetNames.forEach(sheetName => {
                const worksheet = workbook.Sheets[sheetName];
                const sheetData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                const sheetText = sheetData
                    .map((row) => row.join(' | '))
                    .join('\n');
                allText += `\n=== Feuille: ${sheetName} ===\n${sheetText}\n`;
            });
            return {
                text: allText.trim(),
                metadata: {
                    wordCount: this.countWords(allText),
                    fileSize: buffer.length,
                    extractedAt: new Date()
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction Excel: ${error.message}`);
            throw new common_1.BadRequestException('Impossible de lire le fichier Excel');
        }
    }
    extractFromText(buffer) {
        try {
            const text = buffer.toString('utf-8');
            return {
                text,
                metadata: {
                    wordCount: this.countWords(text),
                    fileSize: buffer.length,
                    extractedAt: new Date()
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction texte: ${error.message}`);
            throw new common_1.BadRequestException('Impossible de lire le fichier texte');
        }
    }
    getFileExtension(fileName) {
        const parts = fileName.split('.');
        return parts.length > 1 ? parts[parts.length - 1] : '';
    }
    countWords(text) {
        return text.trim().split(/\s+/).filter(word => word.length > 0).length;
    }
    validateFileType(fileName) {
        const supportedExtensions = ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'txt', 'text'];
        const extension = this.getFileExtension(fileName).toLowerCase();
        return supportedExtensions.includes(extension);
    }
    getSupportedFileTypes() {
        return ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'txt', 'text'];
    }
};
exports.FileExtractionService = FileExtractionService;
exports.FileExtractionService = FileExtractionService = FileExtractionService_1 = __decorate([
    (0, common_1.Injectable)()
], FileExtractionService);
//# sourceMappingURL=file-extraction.service.js.map