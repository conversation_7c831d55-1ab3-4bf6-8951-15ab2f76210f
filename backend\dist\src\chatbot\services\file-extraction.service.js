"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var FileExtractionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileExtractionService = void 0;
const common_1 = require("@nestjs/common");
let FileExtractionService = FileExtractionService_1 = class FileExtractionService {
    logger = new common_1.Logger(FileExtractionService_1.name);
    supportedTypes = [
        'pdf',
        'docx',
        'doc',
        'xlsx',
        'xls',
        'txt',
        'text'
    ];
    getSupportedFileTypes() {
        return [...this.supportedTypes];
    }
    validateFileType(fileName) {
        const extension = fileName.split('.').pop()?.toLowerCase();
        return extension ? this.supportedTypes.includes(extension) : false;
    }
    async extractContent(buffer, fileName, mimeType) {
        try {
            this.logger.log(`Extraction du contenu du fichier: ${fileName}`);
            const extension = fileName.split('.').pop()?.toLowerCase();
            let text = '';
            switch (extension) {
                case 'txt':
                case 'text':
                    text = buffer.toString('utf-8');
                    break;
                case 'pdf':
                    text = await this.extractFromPDF(buffer);
                    break;
                case 'docx':
                case 'doc':
                    text = await this.extractFromWord(buffer);
                    break;
                case 'xlsx':
                case 'xls':
                    text = await this.extractFromExcel(buffer);
                    break;
                default:
                    throw new Error(`Type de fichier non supporté: ${extension}`);
            }
            const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
            return {
                text,
                metadata: {
                    wordCount,
                    extractedAt: new Date(),
                    fileSize: buffer.length,
                    fileType: extension
                }
            };
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'extraction: ${error.message}`);
            throw error;
        }
    }
    async extractFromPDF(buffer) {
        this.logger.warn('Extraction PDF non implémentée - retour d\'un placeholder');
        return `[Contenu PDF extrait du fichier - ${buffer.length} bytes]\n\nCeci est un placeholder pour l'extraction PDF. Pour une vraie implémentation, installez 'pdf-parse' et implémentez l'extraction.`;
    }
    async extractFromWord(buffer) {
        this.logger.warn('Extraction Word non implémentée - retour d\'un placeholder');
        return `[Contenu Word extrait du fichier - ${buffer.length} bytes]\n\nCeci est un placeholder pour l'extraction Word. Pour une vraie implémentation, installez 'mammoth' et implémentez l'extraction.`;
    }
    async extractFromExcel(buffer) {
        this.logger.warn('Extraction Excel non implémentée - retour d\'un placeholder');
        return `[Contenu Excel extrait du fichier - ${buffer.length} bytes]\n\nCeci est un placeholder pour l'extraction Excel. Pour une vraie implémentation, installez 'xlsx' et implémentez l'extraction.`;
    }
};
exports.FileExtractionService = FileExtractionService;
exports.FileExtractionService = FileExtractionService = FileExtractionService_1 = __decorate([
    (0, common_1.Injectable)()
], FileExtractionService);
//# sourceMappingURL=file-extraction.service.js.map