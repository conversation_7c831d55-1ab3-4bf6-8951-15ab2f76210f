"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const chatbot_service_1 = require("./chatbot.service");
const chatbot_message_dto_1 = require("./dto/chatbot-message.dto");
let ChatbotController = class ChatbotController {
    chatbotService;
    constructor(chatbotService) {
        this.chatbotService = chatbotService;
    }
    async sendMessage(dto) {
        try {
            const response = await this.chatbotService.processMessage(dto.message);
            return { response };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors du traitement du message: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async executeQuery(query) {
        try {
            return await this.chatbotService.executeQuery(query);
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de l'exécution de la requête: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async uploadAndProcessFile(file, dto) {
        try {
            if (!file) {
                throw new common_1.BadRequestException('Aucun fichier fourni');
            }
            const result = await this.chatbotService.processFileAndSummarize(file.buffer, file.originalname, dto.fileName || file.originalname, file.mimetype);
            const formattedResponse = this.chatbotService.formatSummaryResponse(result.summary, file.originalname);
            return {
                success: true,
                fileName: file.originalname,
                fileSize: file.size,
                extractedContent: {
                    wordCount: result.extractedContent.metadata?.wordCount,
                    pageCount: result.extractedContent.metadata?.pageCount,
                    extractedAt: result.extractedContent.metadata?.extractedAt
                },
                summary: result.summary,
                formattedResponse
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors du traitement du fichier: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async summarizeText(dto) {
        try {
            if (!dto.content || dto.content.trim().length === 0) {
                throw new common_1.BadRequestException('Le contenu à résumer ne peut pas être vide');
            }
            const result = await this.chatbotService.processTextInput(dto.content, dto.context);
            const formattedResponse = this.chatbotService.formatSummaryResponse(result.summary, dto.fileName);
            return {
                success: true,
                summary: result.summary,
                quickSummary: result.quickSummary,
                formattedResponse
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de la génération du résumé: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async extractFileContent(file, dto) {
        try {
            if (!file) {
                throw new common_1.BadRequestException('Aucun fichier fourni');
            }
            const extractedContent = await this.chatbotService.processFileContent(file.buffer, file.originalname, file.mimetype);
            return {
                success: true,
                fileName: file.originalname,
                fileSize: file.size,
                extractedContent: {
                    text: extractedContent.text,
                    metadata: extractedContent.metadata
                }
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de l'extraction du contenu: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSupportedFormats() {
        return {
            supportedFormats: this.chatbotService['fileExtractionService'].getSupportedFileTypes(),
            maxFileSize: '10MB',
            description: 'Formats de fichiers supportés pour l\'extraction de contenu et la génération de résumés'
        };
    }
};
exports.ChatbotController = ChatbotController;
__decorate([
    (0, common_1.Post)('message'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.ChatbotMessageDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Post)('query'),
    __param(0, (0, common_1.Body)('query')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "executeQuery", null);
__decorate([
    (0, common_1.Post)('upload-file'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: {
            fileSize: 10 * 1024 * 1024,
        },
        fileFilter: (req, file, callback) => {
            const allowedMimes = [
                'application/pdf',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'text/plain'
            ];
            if (allowedMimes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new common_1.BadRequestException('Type de fichier non supporté'), false);
            }
        }
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chatbot_message_dto_1.FileProcessingDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "uploadAndProcessFile", null);
__decorate([
    (0, common_1.Post)('summarize-text'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.SummarizationRequestDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "summarizeText", null);
__decorate([
    (0, common_1.Post)('extract-content'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: {
            fileSize: 10 * 1024 * 1024,
        }
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, chatbot_message_dto_1.FileProcessingDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "extractFileContent", null);
__decorate([
    (0, common_1.Post)('supported-formats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "getSupportedFormats", null);
exports.ChatbotController = ChatbotController = __decorate([
    (0, common_1.Controller)('chatbot'),
    __metadata("design:paramtypes", [chatbot_service_1.ChatbotService])
], ChatbotController);
//# sourceMappingURL=chatbot.controller.js.map