"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotController = void 0;
const common_1 = require("@nestjs/common");
const chatbot_service_1 = require("./chatbot.service");
const chatbot_message_dto_1 = require("./dto/chatbot-message.dto");
let ChatbotController = class ChatbotController {
    chatbotService;
    constructor(chatbotService) {
        this.chatbotService = chatbotService;
    }
    async sendMessage(dto) {
        try {
            let userId = dto.userId;
            if (typeof dto.userId === 'string') {
                userId = parseInt(dto.userId, 10);
                if (isNaN(userId)) {
                    userId = undefined;
                }
            }
            const response = await this.chatbotService.processMessage(dto.message, dto.sessionId, userId);
            return {
                response,
                sessionId: dto.sessionId
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors du traitement du message: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMemoryHistory(dto) {
        try {
            const history = await this.chatbotService.getMemoryHistory(dto.sessionId, dto.userId, dto.limit || 10);
            return {
                success: true,
                sessionId: dto.sessionId,
                history,
                count: history.length
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de la récupération de l'historique: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async clearMemory(dto) {
        try {
            const success = await this.chatbotService.clearMemory(dto.sessionId, dto.userId);
            return {
                success,
                sessionId: dto.sessionId,
                message: success ? 'Mémoire effacée avec succès' : 'Erreur lors de l\'effacement de la mémoire'
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de l'effacement de la mémoire: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMemoryContext(dto) {
        try {
            const context = await this.chatbotService.getMemoryContext(dto.sessionId, dto.userId, dto.limit || 5);
            return {
                success: true,
                sessionId: dto.sessionId,
                context,
                hasContext: context.length > 0
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Erreur lors de la récupération du contexte: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async testDatabase() {
        try {
            const result = await this.chatbotService.testDatabaseConnection();
            return {
                success: true,
                result
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message,
                stack: error.stack
            };
        }
    }
    async getUsers() {
        try {
            const users = await this.chatbotService.getUsers();
            return {
                success: true,
                users
            };
        }
        catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
};
exports.ChatbotController = ChatbotController;
__decorate([
    (0, common_1.Post)('message'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.ChatbotMessageDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Post)('memory/history'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.ChatMemoryDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "getMemoryHistory", null);
__decorate([
    (0, common_1.Post)('memory/clear'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.ClearMemoryDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "clearMemory", null);
__decorate([
    (0, common_1.Post)('memory/context'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chatbot_message_dto_1.ChatMemoryDto]),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "getMemoryContext", null);
__decorate([
    (0, common_1.Post)('debug/test-db'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "testDatabase", null);
__decorate([
    (0, common_1.Post)('debug/users'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatbotController.prototype, "getUsers", null);
exports.ChatbotController = ChatbotController = __decorate([
    (0, common_1.Controller)('chatbot'),
    __metadata("design:paramtypes", [chatbot_service_1.ChatbotService])
], ChatbotController);
//# sourceMappingURL=chatbot.controller.js.map