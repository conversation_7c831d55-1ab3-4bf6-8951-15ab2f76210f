"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ChatbotService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatbotService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
const axios_1 = require("axios");
const DATABASE_ENTITIES = {
    user: {
        name: 'utilisateur',
        plural: 'utilisateurs',
        keywords: [
            'user', 'users', 'account', 'profile', 'student', 'instructor', 'admin',
            'utilisateur', 'utilisateurs', 'compte', 'email', 'profil', 'étudiant', 'formateur', 'admin',
            'مستخدم', 'مستخدمين', 'حساب', 'ملف', 'طالب', 'مدرس', 'مشرف'
        ],
        displayField: 'name',
        include: {
            formateurs: true,
            Etudiants: true,
            Admins: true,
            sentFeedback: true,
            receivedFeedback: true
        }
    },
    program: {
        name: 'programme',
        plural: 'programmes',
        keywords: [
            'program', 'programs', 'training', 'curriculum',
            'programme', 'programmes', 'formation', 'cursus',
            'برنامج', 'برامج', 'تدريب', 'منهج'
        ],
        displayField: 'name',
        include: {
            modules: {
                include: {
                    module: {
                        include: {
                            courses: {
                                include: {
                                    course: {
                                        include: {
                                            courseContenus: {
                                                include: {
                                                    contenu: true
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    module: {
        name: 'module',
        plural: 'modules',
        keywords: ['module', 'modules', 'chapitre', 'section', 'unité'],
        displayField: 'name',
        include: {
            courses: {
                include: {
                    course: {
                        include: {
                            courseContenus: {
                                include: {
                                    contenu: true
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    course: {
        name: 'cours',
        plural: 'cours',
        keywords: [
            'course', 'courses', 'lesson', 'subject',
            'cours', 'leçon', 'matière',
            'دورة', 'دورات', 'درس', 'مادة'
        ],
        displayField: 'title',
        include: {
            courseContenus: {
                include: {
                    contenu: {
                        include: {
                            quiz: {
                                include: {
                                    questions: true
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    contenu: {
        name: 'contenu',
        plural: 'contenus',
        keywords: ['contenu', 'contenus', 'document', 'ressource', 'fichier', 'pdf', 'video', 'image'],
        displayField: 'title',
        include: {
            quiz: {
                include: {
                    questions: {
                        include: {
                            answers: true
                        }
                    }
                }
            }
        }
    },
    quiz: {
        name: 'quiz',
        plural: 'quiz',
        keywords: ['quiz', 'quizzes', 'test', 'évaluation', 'question', 'examen'],
        displayField: 'title',
        include: {
            questions: {
                include: {
                    answers: true
                }
            },
            contenu: true
        }
    },
    feedback: {
        name: 'feedback',
        plural: 'feedbacks',
        keywords: ['feedback', 'avis', 'commentaire', 'retour', 'évaluation'],
        displayField: 'content',
        include: {
            sender: true,
            receiver: true,
            responses: true
        }
    }
};
const RESPONSES = {
    greeting: "Bonjour ! Je suis l'assistant de la plateforme LMS MKA. Comment puis-je vous aider aujourd'hui ?",
    help: "Je peux vous fournir des informations sur :\n• Les utilisateurs et leurs rôles\n• Les programmes de formation\n• Les modules et cours\n• Les contenus et quiz\n• Les statistiques de la plateforme\n\nPosez-moi une question !",
    notUnderstood: "Je ne comprends pas votre demande. Tapez 'aide' pour voir ce que je peux faire.",
    databaseError: "Désolé, je ne peux pas accéder aux données pour le moment. La base de données n'est pas disponible.",
    noData: "Aucune donnée trouvée pour votre recherche."
};
function normalize(str) {
    return str
        .replace(/[''`]/g, "'")
        .replace(/\s+/g, ' ')
        .toLowerCase()
        .trim();
}
let ChatbotService = ChatbotService_1 = class ChatbotService {
    prisma;
    logger = new common_1.Logger(ChatbotService_1.name);
    groqApiKey = process.env.GROQ_API_KEY;
    groqModel = 'llama-3.3-70b-versatile';
    constructor(prisma) {
        this.prisma = prisma;
    }
    async processMessage(message, sessionId, userId) {
        try {
            this.logger.log(`🚀 Processing message: "${message}" with sessionId: ${sessionId}, userId: ${userId}`);
            const normalizedMsg = normalize(message);
            this.logger.log(`🔄 Normalized message: "${normalizedMsg}"`);
            if (this.isGreeting(normalizedMsg)) {
                let response = RESPONSES.greeting;
                this.logger.log(`✅ Greeting detected, response: ${response.substring(0, 50)}...`);
                if (sessionId) {
                    this.logger.log(`💾 Calling saveToMemory for greeting...`);
                    try {
                        await this.saveToMemory(sessionId, userId, message, response);
                        response += " [MEMORY: SAVED]";
                    }
                    catch (error) {
                        response += ` [MEMORY: ERROR - ${error.message}]`;
                    }
                }
                else {
                    response += " [MEMORY: NO SESSION]";
                }
                return response;
            }
            if (this.isHelpRequest(normalizedMsg)) {
                const response = this.getEnhancedHelp();
                if (sessionId) {
                    await this.saveToMemory(sessionId, userId, message, response);
                }
                return response;
            }
            if (this.isFileHelpRequest(normalizedMsg)) {
                const response = this.getFileProcessingHelp();
                if (sessionId) {
                    await this.saveToMemory(sessionId, userId, message, response);
                }
                return response;
            }
            if (this.isStatsRequest(normalizedMsg)) {
                const response = await this.getGeneralStats();
                if (sessionId) {
                    await this.saveToMemory(sessionId, userId, message, response);
                }
                return response;
            }
            this.logger.log(`🔍 Checking entity queries...`);
            const entityResponse = await this.handleEntityQueries(normalizedMsg);
            if (entityResponse) {
                this.logger.log(`✅ Entity response found: ${entityResponse.substring(0, 100)}...`);
                if (sessionId) {
                    await this.saveToMemory(sessionId, userId, message, entityResponse);
                }
                return entityResponse;
            }
            else {
                this.logger.log(`❌ No entity response found, continuing to other handlers...`);
            }
            const complexResponse = await this.handleComplexQueries(normalizedMsg);
            if (complexResponse) {
                if (sessionId) {
                    await this.saveToMemory(sessionId, userId, message, complexResponse);
                }
                return complexResponse;
            }
            if (this.groqApiKey) {
                const groqResponse = await this.askGroqWithMemory(message, sessionId, userId);
                if (groqResponse) {
                    if (sessionId) {
                        await this.saveToMemory(sessionId, userId, message, groqResponse);
                    }
                    return groqResponse;
                }
            }
            const response = RESPONSES.notUnderstood;
            if (sessionId) {
                await this.saveToMemory(sessionId, userId, message, response);
            }
            return response;
        }
        catch (error) {
            this.logger.error(`Erreur lors du traitement du message: ${error.message}`);
            return RESPONSES.databaseError;
        }
    }
    isGreeting(msg) {
        const greetings = [
            'hello', 'hi', 'hey',
            'bonjour', 'salut', 'bonsoir',
            'مرحبا', 'أهلا', 'السلام عليكم', 'عسلامة', 'اهلا'
        ];
        return greetings.some(greeting => {
            const regex = new RegExp(`\\b${greeting}\\b`, 'i');
            return regex.test(msg);
        });
    }
    isHelpRequest(msg) {
        const helpKeywords = ['aide', 'help', 'aidez-moi', 'que peux-tu faire', 'what can you do'];
        return helpKeywords.some(keyword => msg.includes(keyword));
    }
    isFileHelpRequest(msg) {
        const fileKeywords = ['fichier', 'file', 'document', 'résumé', 'summary', 'upload', 'télécharger', 'analyser'];
        return fileKeywords.some(keyword => msg.includes(keyword));
    }
    getEnhancedHelp() {
        return RESPONSES.help + "\n\n" +
            "**Fonctionnalités disponibles:**\n" +
            "• 🧠 Mémoire de conversation intelligente\n" +
            "• � Statistiques de la plateforme\n" +
            "• 🔍 Recherche dans la base de données\n" +
            "• � Conversations contextuelles\n\n" +
            "Tapez 'aide' pour plus d'informations !";
    }
    getFileProcessingHelp() {
        return "📄 **Fonctionnalités de traitement de fichiers**\n\n" +
            "Les fonctionnalités de traitement de fichiers ne sont pas encore disponibles.\n" +
            "Pour l'instant, je peux vous aider avec :\n" +
            "• Les informations sur la plateforme\n" +
            "• Les statistiques des utilisateurs\n" +
            "• Les questions générales sur l'éducation\n\n" +
            "Posez-moi une question !";
    }
    isStatsRequest(msg) {
        const statsKeywords = ['statistiques', 'stats', 'résumé', 'overview', 'dashboard'];
        return statsKeywords.some(keyword => msg.includes(keyword));
    }
    async getGeneralStats() {
        try {
            const [userCount, programCount, moduleCount, courseCount, contenuCount, quizCount] = await Promise.all([
                this.prisma.user.count(),
                this.prisma.program.count(),
                this.prisma.module.count(),
                this.prisma.course.count(),
                this.prisma.contenu.count(),
                this.prisma.quiz.count()
            ]);
            return `📊 **Statistiques de la plateforme MKA LMS**\n\n` +
                `👥 Utilisateurs: ${userCount}\n` +
                `📚 Programmes: ${programCount}\n` +
                `📖 Modules: ${moduleCount}\n` +
                `🎓 Cours: ${courseCount}\n` +
                `📄 Contenus: ${contenuCount}\n` +
                `❓ Quiz: ${quizCount}`;
        }
        catch (error) {
            this.logger.error(`Erreur lors de la récupération des statistiques: ${error.message}`);
            return RESPONSES.databaseError;
        }
    }
    async handleEntityQueries(msg) {
        this.logger.log(`🔍 Analyzing message: "${msg}"`);
        for (const [entityKey, entity] of Object.entries(DATABASE_ENTITIES)) {
            const hasKeyword = entity.keywords.some(keyword => msg.includes(keyword));
            this.logger.log(`🔎 Entity ${entityKey}: hasKeyword=${hasKeyword}, keywords=${entity.keywords.join(', ')}`);
            if (!hasKeyword)
                continue;
            try {
                if (this.isCountQuery(msg)) {
                    this.logger.log(`📊 Count query detected for entity: ${entityKey}`);
                    const count = await this.prisma[entityKey].count();
                    this.logger.log(`📊 Count result for ${entityKey}: ${count}`);
                    return `Il y a actuellement **${count}** ${entity.plural} dans la base de données.`;
                }
                if (this.isListQuery(msg)) {
                    const items = await this.prisma[entityKey].findMany({
                        take: 10,
                        include: entity.include || {}
                    });
                    if (items.length === 0) {
                        return `Aucun ${entity.name} trouvé dans la base de données.`;
                    }
                    return this.formatEntityList(items, entity, entityKey);
                }
                if (this.isDetailQuery(msg)) {
                    const item = await this.prisma[entityKey].findFirst({
                        include: entity.include || {}
                    });
                    if (!item) {
                        return `Aucun ${entity.name} trouvé dans la base de données.`;
                    }
                    return this.formatEntityDetail(item, entity, entityKey);
                }
                if (this.isSearchQuery(msg)) {
                    const searchTerm = this.extractSearchTerm(msg);
                    if (searchTerm) {
                        const items = await this.searchEntity(entityKey, entity, searchTerm);
                        if (items.length === 0) {
                            return `Aucun ${entity.name} trouvé avec le terme "${searchTerm}".`;
                        }
                        return this.formatSearchResults(items, entity, searchTerm);
                    }
                }
            }
            catch (error) {
                this.logger.error(`Erreur lors de la requête ${entityKey}: ${error.message}`);
                return `Désolé, je ne peux pas accéder aux informations sur les ${entity.plural} pour le moment.`;
            }
        }
        return null;
    }
    async handleComplexQueries(msg) {
        try {
            if (msg.includes('statistique') || msg.includes('stats') || msg.includes('résumé')) {
                return await this.getAdvancedStats();
            }
            if (msg.includes('qui enseigne') || msg.includes('formateur')) {
                return await this.getInstructorInfo(msg);
            }
            if (msg.includes('populaire') || msg.includes('plus suivi')) {
                return await this.getPopularPrograms();
            }
            if (msg.includes('quiz difficile') || msg.includes('évaluation')) {
                return await this.getQuizStats();
            }
            if (msg.includes('récent') || msg.includes('nouveau') || msg.includes('dernière')) {
                return await this.getRecentActivity();
            }
        }
        catch (error) {
            this.logger.error(`Erreur dans handleComplexQueries: ${error.message}`);
        }
        return null;
    }
    isCountQuery(msg) {
        const countKeywords = [
            'combien', 'nombre',
            'how many', 'count',
            'كم', 'عدد', 'قداش', 'شحال'
        ];
        return countKeywords.some(keyword => msg.includes(keyword));
    }
    isListQuery(msg) {
        const listKeywords = [
            'liste', 'affiche', 'montre', 'nom de', 'nom du', 'noms', 'je veux le nom', 'donne moi le nom', 'quels sont',
            'list', 'show', 'name of', 'names', 'what are', 'give me the name',
            'قائمة', 'اعرض', 'أظهر', 'اسم', 'أسماء', 'ما هي'
        ];
        return listKeywords.some(keyword => msg.includes(keyword));
    }
    isDetailQuery(msg) {
        const detailKeywords = ['détail', 'detail', 'info', 'information'];
        return detailKeywords.some(keyword => msg.includes(keyword));
    }
    async askGroq(message) {
        try {
            if (!this.groqApiKey) {
                this.logger.warn('Clé API Groq non configurée');
                return null;
            }
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    {
                        role: 'system',
                        content: 'Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Réponds de façon concise et précise en français. Tu peux aider avec des questions sur l\'éducation, la formation, et la gestion de contenu pédagogique.'
                    },
                    { role: 'user', content: message }
                ],
                temperature: 0.7,
                max_tokens: 1000,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 15000,
            });
            return response.data?.choices?.[0]?.message?.content || null;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'appel à Groq: ${error.message}`);
            if (error.response) {
                this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
            }
            return null;
        }
    }
    async askGroqWithMemory(message, sessionId, userId) {
        try {
            if (!this.groqApiKey) {
                this.logger.warn('Clé API Groq non configurée');
                return null;
            }
            let contextMessage = '';
            if (sessionId) {
                contextMessage = await this.getMemoryContext(sessionId, userId, 3);
                this.logger.log(`🧠 Context retrieved for session ${sessionId}: "${contextMessage.substring(0, 100)}..."`);
            }
            const systemContent = `Tu es un assistant intelligent pour une plateforme LMS appelée MKA. Tu as accès à toute la base de données et peux répondre aux questions sur:

📚 **DONNÉES DISPONIBLES:**
• **Utilisateurs** - Étudiants, Formateurs, Admins, Créateurs de formation
• **Programmes** - Formations complètes avec modules et cours
• **Modules** - Sections de formation avec cours associés
• **Cours** - Leçons individuelles avec contenus
• **Contenus** - PDFs, vidéos, images, quiz, exercices
• **Quiz** - Tests avec questions et réponses
• **Feedback** - Avis et commentaires entre utilisateurs
• **Sessions** - Sessions de formation en cours

🔍 **CAPACITÉS:**
• Compter les éléments ("combien d'utilisateurs")
• Lister les éléments ("liste des programmes")
• Chercher par nom ("trouve le cours JavaScript")
• Afficher les détails ("détails du programme")
• Analyser les relations entre entités

Réponds de façon concise et précise en français. Utilise les données réelles de la base de données pour tes réponses.` +
                (contextMessage ? '\n\n**CONTEXTE DE LA CONVERSATION:**\n' + contextMessage : '');
            this.logger.log(`🤖 Sending to Groq with system content length: ${systemContent.length}`);
            const response = await axios_1.default.post('https://api.groq.com/openai/v1/chat/completions', {
                model: this.groqModel,
                messages: [
                    {
                        role: 'system',
                        content: systemContent
                    },
                    { role: 'user', content: message }
                ],
                temperature: 0.7,
                max_tokens: 1000,
            }, {
                headers: {
                    Authorization: `Bearer ${this.groqApiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 15000,
            });
            return response.data?.choices?.[0]?.message?.content || null;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'appel à Groq avec mémoire: ${error.message}`);
            if (error.response) {
                this.logger.error(`Détails de l'erreur: ${JSON.stringify(error.response.data)}`);
            }
            return null;
        }
    }
    async executeQuery(query) {
        try {
            if (!query.toLowerCase().trim().startsWith('select')) {
                throw new Error('Seules les requêtes SELECT sont autorisées');
            }
            const results = await this.prisma.$queryRawUnsafe(query);
            return results;
        }
        catch (error) {
            this.logger.error(`Erreur SQL: ${error.message}`);
            return { error: error.message };
        }
    }
    async saveToMemory(sessionId, userId, userMessage, botResponse, context) {
        try {
            let validUserId = null;
            if (userId) {
                const numericUserId = typeof userId === 'string' ? parseInt(userId, 10) : userId;
                if (!isNaN(numericUserId)) {
                    const userExists = await this.prisma.user.findUnique({
                        where: { id: numericUserId }
                    });
                    if (userExists) {
                        validUserId = numericUserId;
                    }
                    else {
                        this.logger.warn(`👤 Utilisateur avec ID ${numericUserId} non trouvé, sauvegarde sans association utilisateur`);
                    }
                }
                else {
                    this.logger.warn(`👤 ID utilisateur invalide: ${userId}, sauvegarde sans association utilisateur`);
                }
            }
            const result = await this.prisma.chatMemory.create({
                data: {
                    sessionId,
                    userId: validUserId,
                    userMessage,
                    botResponse,
                    context
                }
            });
            this.logger.log(`✅ Conversation sauvegardée avec succès - ID: ${result.id}, Session: ${sessionId}, User: ${validUserId}`);
        }
        catch (error) {
            this.logger.error(`❌ Erreur lors de la sauvegarde en mémoire: ${error.message}`);
            this.logger.error(`Stack trace: ${error.stack}`);
            throw error;
        }
    }
    async getMemoryHistory(sessionId, userId, limit = 10) {
        try {
            const history = await this.prisma.chatMemory.findMany({
                where: { sessionId },
                orderBy: { createdAt: 'desc' },
                take: limit,
                select: {
                    id: true,
                    userMessage: true,
                    botResponse: true,
                    context: true,
                    createdAt: true,
                    userId: true
                }
            });
            return history.reverse();
        }
        catch (error) {
            this.logger.error(`Erreur lors de la récupération de l'historique: ${error.message}`);
            return [];
        }
    }
    async clearMemory(sessionId, userId) {
        try {
            const whereClause = { sessionId };
            if (userId) {
                whereClause.userId = userId;
            }
            await this.prisma.chatMemory.deleteMany({
                where: whereClause
            });
            this.logger.log(`Mémoire effacée pour la session: ${sessionId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'effacement de la mémoire: ${error.message}`);
            return false;
        }
    }
    async getMemoryContext(sessionId, userId, limit = 5) {
        try {
            const history = await this.getMemoryHistory(sessionId, userId, limit);
            if (history.length === 0) {
                return '';
            }
            const context = history.map(entry => `Utilisateur: ${entry.userMessage}\nAssistant: ${entry.botResponse}`).join('\n\n');
            return `Contexte de la conversation précédente:\n${context}\n\n`;
        }
        catch (error) {
            this.logger.error(`Erreur lors de la récupération du contexte: ${error.message}`);
            return '';
        }
    }
    async testDatabaseConnection() {
        try {
            const connectionTest = await this.prisma.$queryRaw `SELECT 1 as test`;
            const tableTest = await this.prisma.chatMemory.count();
            const insertTest = await this.prisma.chatMemory.create({
                data: {
                    sessionId: 'test-session',
                    userId: null,
                    userMessage: 'Test message',
                    botResponse: 'Test response',
                    context: 'Test context'
                }
            });
            await this.prisma.chatMemory.delete({
                where: { id: insertTest.id }
            });
            return {
                connectionTest,
                tableTest,
                insertTest: 'SUCCESS',
                message: 'Tous les tests de base de données ont réussi'
            };
        }
        catch (error) {
            throw new Error(`Test de base de données échoué: ${error.message}`);
        }
    }
    async getUsers() {
        try {
            const users = await this.prisma.user.findMany({
                select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true
                }
            });
            return users;
        }
        catch (error) {
            throw new Error(`Erreur lors de la récupération des utilisateurs: ${error.message}`);
        }
    }
    formatEntityList(items, entity, entityKey) {
        const itemList = items.map((item) => {
            let line = `• **${item[entity.displayField] || `ID: ${item.id}`}**`;
            if (entityKey === 'user') {
                line += ` (${item.role || 'Rôle non défini'})`;
                if (item.email)
                    line += ` - ${item.email}`;
            }
            else if (entityKey === 'program') {
                line += ` - ${item.published ? '✅ Publié' : '❌ Non publié'}`;
                if (item.modules?.length)
                    line += ` - ${item.modules.length} modules`;
            }
            else if (entityKey === 'contenu') {
                line += ` - Type: ${item.type || 'Non défini'}`;
                if (item.fileType)
                    line += ` (${item.fileType})`;
                line += ` - ${item.published ? '✅ Publié' : '❌ Non publié'}`;
            }
            else if (entityKey === 'quiz') {
                if (item.questions?.length)
                    line += ` - ${item.questions.length} questions`;
                if (item.timeLimit)
                    line += ` - ${Math.floor(item.timeLimit / 60)} min`;
            }
            return line;
        }).join('\n');
        return `📋 **Liste des ${entity.plural} (${items.length} résultats):**\n\n${itemList}`;
    }
    formatEntityDetail(item, entity, entityKey) {
        let details = `🔍 **Détails du ${entity.name}:**\n\n`;
        details += `**Nom/Titre:** ${item[entity.displayField] || 'Non défini'}\n`;
        details += `**ID:** ${item.id}\n`;
        if (entityKey === 'user') {
            details += `**Email:** ${item.email}\n`;
            details += `**Rôle:** ${item.role}\n`;
            details += `**Actif:** ${item.isActive ? 'Oui' : 'Non'}\n`;
            if (item.phone)
                details += `**Téléphone:** ${item.phone}\n`;
            if (item.location)
                details += `**Localisation:** ${item.location}\n`;
            if (item.skills?.length)
                details += `**Compétences:** ${item.skills.join(', ')}\n`;
        }
        else if (entityKey === 'program') {
            details += `**Publié:** ${item.published ? 'Oui' : 'Non'}\n`;
            if (item.modules?.length) {
                details += `**Modules (${item.modules.length}):**\n`;
                item.modules.forEach((pm) => {
                    details += `  • ${pm.module?.name || 'Module sans nom'}\n`;
                });
            }
        }
        else if (entityKey === 'contenu') {
            details += `**Type:** ${item.type}\n`;
            if (item.fileType)
                details += `**Type de fichier:** ${item.fileType}\n`;
            details += `**Publié:** ${item.published ? 'Oui' : 'Non'}\n`;
            if (item.fileUrl)
                details += `**URL:** ${item.fileUrl}\n`;
            if (item.quiz) {
                details += `**Quiz associé:** ${item.quiz.title || 'Quiz sans titre'}\n`;
                if (item.quiz.questions?.length) {
                    details += `**Nombre de questions:** ${item.quiz.questions.length}\n`;
                }
            }
        }
        return details;
    }
    isSearchQuery(msg) {
        const searchKeywords = ['cherche', 'trouve', 'search', 'find', 'recherche'];
        return searchKeywords.some(keyword => msg.includes(keyword));
    }
    extractSearchTerm(msg) {
        const patterns = [
            /(?:cherche|trouve|search|find|recherche)\s+(.+)/i,
            /"([^"]+)"/,
            /'([^']+)'/
        ];
        for (const pattern of patterns) {
            const match = msg.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        return null;
    }
    async searchEntity(entityKey, entity, searchTerm) {
        const searchField = entity.displayField;
        try {
            return await this.prisma[entityKey].findMany({
                where: {
                    [searchField]: {
                        contains: searchTerm,
                        mode: 'insensitive'
                    }
                },
                include: entity.include || {},
                take: 5
            });
        }
        catch (error) {
            this.logger.error(`Erreur lors de la recherche dans ${entityKey}: ${error.message}`);
            return [];
        }
    }
    formatSearchResults(items, entity, searchTerm) {
        const results = items.map((item) => `• **${item[entity.displayField]}** (ID: ${item.id})`).join('\n');
        return `🔍 **Résultats de recherche pour "${searchTerm}" dans ${entity.plural}:**\n\n${results}`;
    }
    async getAdvancedStats() {
        try {
            const [userCount, programCount, moduleCount, courseCount, contenuCount, quizCount] = await Promise.all([
                this.prisma.user.count(),
                this.prisma.program.count(),
                this.prisma.module.count(),
                this.prisma.course.count(),
                this.prisma.contenu.count(),
                this.prisma.quiz.count()
            ]);
            const [publishedPrograms, publishedContent] = await Promise.all([
                this.prisma.program.count({ where: { published: true } }),
                this.prisma.contenu.count({ where: { published: true } })
            ]);
            const usersByRole = await this.prisma.user.groupBy({
                by: ['role'],
                _count: { role: true }
            });
            let stats = `📊 **Statistiques de la plateforme MKA:**\n\n`;
            stats += `👥 **Utilisateurs:** ${userCount} total\n`;
            usersByRole.forEach(group => {
                stats += `   • ${group.role}: ${group._count.role}\n`;
            });
            stats += `\n📚 **Contenu pédagogique:**\n`;
            stats += `   • Programmes: ${programCount} (${publishedPrograms} publiés)\n`;
            stats += `   • Modules: ${moduleCount}\n`;
            stats += `   • Cours: ${courseCount}\n`;
            stats += `   • Contenus: ${contenuCount} (${publishedContent} publiés)\n`;
            stats += `   • Quiz: ${quizCount}\n`;
            return stats;
        }
        catch (error) {
            this.logger.error(`Erreur dans getAdvancedStats: ${error.message}`);
            return "Désolé, je ne peux pas récupérer les statistiques pour le moment.";
        }
    }
    async getInstructorInfo(msg) {
        try {
            const instructors = await this.prisma.user.findMany({
                where: { role: 'Formateur' },
                include: {
                    formateurs: true
                },
                take: 5
            });
            if (instructors.length === 0) {
                return "Aucun formateur trouvé dans la base de données.";
            }
            let info = `👨‍🏫 **Formateurs de la plateforme:**\n\n`;
            instructors.forEach(instructor => {
                info += `• **${instructor.name || instructor.email}**\n`;
                if (instructor.formateurs[0]?.speciality) {
                    info += `  Spécialité: ${instructor.formateurs[0].speciality}\n`;
                }
                if (instructor.email)
                    info += `  Email: ${instructor.email}\n`;
                info += `\n`;
            });
            return info;
        }
        catch (error) {
            this.logger.error(`Erreur dans getInstructorInfo: ${error.message}`);
            return "Désolé, je ne peux pas récupérer les informations des formateurs.";
        }
    }
    async getPopularPrograms() {
        try {
            const programs = await this.prisma.program.findMany({
                where: { published: true },
                include: {
                    modules: {
                        include: {
                            module: {
                                include: {
                                    courses: {
                                        include: {
                                            course: true
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                take: 5
            });
            if (programs.length === 0) {
                return "Aucun programme publié trouvé.";
            }
            let info = `🏆 **Programmes de formation disponibles:**\n\n`;
            programs.forEach(program => {
                const moduleCount = program.modules.length;
                const courseCount = program.modules.reduce((total, pm) => total + pm.module.courses.length, 0);
                info += `• **${program.name}**\n`;
                info += `  📚 ${moduleCount} modules, ${courseCount} cours\n`;
                info += `  ✅ Publié\n\n`;
            });
            return info;
        }
        catch (error) {
            this.logger.error(`Erreur dans getPopularPrograms: ${error.message}`);
            return "Désolé, je ne peux pas récupérer les informations des programmes.";
        }
    }
    async getQuizStats() {
        try {
            const quizzes = await this.prisma.quiz.findMany({
                include: {
                    questions: true,
                    contenu: true
                },
                take: 5
            });
            if (quizzes.length === 0) {
                return "Aucun quiz trouvé dans la base de données.";
            }
            let info = `🧠 **Quiz disponibles:**\n\n`;
            quizzes.forEach(quiz => {
                info += `• **${quiz.title || 'Quiz sans titre'}**\n`;
                info += `  📝 ${quiz.questions.length} questions\n`;
                if (quiz.timeLimit) {
                    info += `  ⏱️ Durée: ${Math.floor(quiz.timeLimit / 60)} minutes\n`;
                }
                if (quiz.contenu) {
                    info += `  📄 Contenu: ${quiz.contenu.title}\n`;
                }
                info += `\n`;
            });
            return info;
        }
        catch (error) {
            this.logger.error(`Erreur dans getQuizStats: ${error.message}`);
            return "Désolé, je ne peux pas récupérer les informations des quiz.";
        }
    }
    async getRecentActivity() {
        try {
            const recentUsers = await this.prisma.user.findMany({
                orderBy: { createdAt: 'desc' },
                take: 3,
                select: {
                    name: true,
                    email: true,
                    role: true,
                    createdAt: true
                }
            });
            const recentPrograms = await this.prisma.program.findMany({
                orderBy: { id: 'desc' },
                take: 3,
                select: {
                    name: true,
                    published: true
                }
            });
            let info = `🕒 **Activité récente:**\n\n`;
            if (recentUsers.length > 0) {
                info += `👥 **Nouveaux utilisateurs:**\n`;
                recentUsers.forEach(user => {
                    const date = new Date(user.createdAt).toLocaleDateString('fr-FR');
                    info += `• ${user.name || user.email} (${user.role}) - ${date}\n`;
                });
                info += `\n`;
            }
            if (recentPrograms.length > 0) {
                info += `📚 **Programmes récents:**\n`;
                recentPrograms.forEach(program => {
                    info += `• ${program.name} ${program.published ? '✅' : '❌'}\n`;
                });
            }
            return info;
        }
        catch (error) {
            this.logger.error(`Erreur dans getRecentActivity: ${error.message}`);
            return "Désolé, je ne peux pas récupérer l'activité récente.";
        }
    }
};
exports.ChatbotService = ChatbotService;
exports.ChatbotService = ChatbotService = ChatbotService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], ChatbotService);
//# sourceMappingURL=chatbot.service.js.map